package com.qunar.strategy.framework.spring.boot.autoconfigure;

import com.qunar.strategy.framework.core.annotation.EnableUnitExecutorScan;
import com.qunar.strategy.framework.core.config.StrategyConfiguration;
import com.qunar.strategy.framework.core.config.StrategyRouteManager;
import com.qunar.strategy.framework.core.engine.StrategyEngine;
import com.qunar.strategy.framework.core.executor.ExecutorRepository;
import com.qunar.strategy.framework.context.StrategyContextBuilder;
import com.qunar.strategy.framework.context.StrategyContextService;
import com.qunar.strategy.framework.core.executor.UnitExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * 传统Spring MVC配置适配器
 * 不依赖Spring Boot自动配置机制
 */
@Configuration
@EnableUnitExecutorScan
public class StrategyMvcConfiguration {

    private static final Logger log = LoggerFactory.getLogger(StrategyMvcConfiguration.class);

    @Value("${strategy.framework.config-location-folder:classpath:strategy/strategies/}")
    private String configLocationFolder;

    @Value("${strategy.framework.route-config-location:classpath:strategy/routes.json}")
    private String routeConfigLocation;

    @Value("${strategy.framework.route-config-remote:routes.json}")
    private String routeConfigRemote;

    @Value("${strategy.framework.enable-remote-config:true}")
    private boolean enableRemoteConfig;

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void init() {
        Map<String, UnitExecutor> executors = applicationContext.getBeansOfType(UnitExecutor.class);
        log.info("Found {} UnitExecutor implementations:", executors.size());
        executors.forEach((name, executor) -> {
            log.info("UnitExecutor implementations detail  {} => {}", name, executor.getClass().getName());
        });
    }

    @Bean
    public StrategyConfiguration strategyConfiguration() {
        StrategyConfiguration configuration = new StrategyConfiguration(
                configLocationFolder,
                routeConfigLocation,
                routeConfigRemote,
                enableRemoteConfig
        );
        
        log.info("Initialized StrategyConfiguration with config location: {}, route config location: {}", 
                configLocationFolder, routeConfigLocation);
        return configuration;
    }
    
    @Bean
    public StrategyRouteManager strategyRouteManager(ResourceLoader resourceLoader, StrategyConfiguration strategyConfiguration) {
        StrategyRouteManager routeManager = new StrategyRouteManager(resourceLoader, strategyConfiguration);
        log.info("Initialized StrategyRouteManager");
        return routeManager;
    }

    @Bean
    public ExecutorRepository executorRegistry() {
        ExecutorRepository registry = new ExecutorRepository(applicationContext);
        log.info("Initialized ExecutorRegistry");
        return registry;
    }

    @Bean
    public StrategyEngine strategyEngine(ExecutorRepository executorRepository, StrategyRouteManager strategyRouteManager) {
        StrategyEngine engine = new StrategyEngine(executorRepository, strategyRouteManager);
        log.info("Initialized StrategyEngine");
        return engine;
    }

    @Bean
    public StrategyContextBuilder strategyContextBuilder() {
        StrategyContextBuilder builder = new StrategyContextBuilder();
        log.info("Initialized StrategyContextBuilder");
        return builder;
    }

    @Bean
    public StrategyContextService strategyContextService(
            StrategyContextBuilder contextBuilder) {
        StrategyContextService service = new StrategyContextService(contextBuilder);
        log.info("Initialized StrategyContextService");
        return service;
    }
} 