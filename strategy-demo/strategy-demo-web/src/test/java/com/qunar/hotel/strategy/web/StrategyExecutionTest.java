package com.qunar.hotel.strategy.web;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.qunar.strategy.framework.context.StrategyContextService;
import com.qunar.strategy.framework.core.engine.StrategyEngine;
import com.qunar.strategy.framework.common.model.ExecutionContext;
import com.qunar.strategy.framework.context.StrategyContextService.DeviceInfo;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest
class StrategyExecutionTest {

    @Autowired
    private StrategyContextService strategyContextService;

    @Autowired
    private StrategyEngine strategyEngine;

    @Test
    void testStrategyExecution() throws Exception {
        // 准备测试数据
        String userId = "test-user";
        String userType = "VIP";
        
        Set<String> userIdentities = new HashSet<>();
        userIdentities.add("oldUser");

        DeviceInfo deviceInfo = new DeviceInfo();
//        deviceInfo.setDeviceType("mobile");
        deviceInfo.setPlatform("ios");
        deviceInfo.setVersion("14.0");
        
        Map<String, Object> businessParams = new HashMap<>();
        businessParams.put("channel", "app");
        businessParams.put("needRecall", true);
        businessParams.put("includeBooks", true);

        ExecutionContext context = strategyContextService.buildBasicContext("traceId", "flowId", userId, userType, false);

        // 构建上下文
        strategyContextService.buildFullContext(
                context,
                userIdentities,
                deviceInfo,
                businessParams
        );
        context.setVariable("isWeek", true);
        Map<String,String> abtest = Maps.newHashMap();
        abtest.put("test_exp_1", "A");
        context.setVariable("abTest", abtest);

        // 验证上下文构建
        assertNotNull(context, "Context should not be null");
        assertEquals(userId, context.getUserId(), "User ID should match");
        assertEquals(userType, context.getVariables().get("userType"), "User type should match");

        context.setResult("test-result", Lists.newArrayList("123"));
        // 识别策略
        strategyEngine.execute(context);
    }

}