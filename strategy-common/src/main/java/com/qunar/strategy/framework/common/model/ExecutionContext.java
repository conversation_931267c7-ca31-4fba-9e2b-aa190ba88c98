package com.qunar.strategy.framework.common.model;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ExecutionContext {
    private String strategyId;
    private String currentStageId;
    private String currentUnitId;
    private boolean debugMode = false;
    private final Map<String, Object> variables;
    private final Map<String, Object> results;
    private final Map<String, Object> debugInfo;

    public ExecutionContext() {
        this.variables = new ConcurrentHashMap<>();
        this.results = new ConcurrentHashMap<>();
        this.debugInfo = new ConcurrentHashMap<>();
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void setVariable(String key, Object value) {
        variables.put(key, value);
    }

    @SuppressWarnings("unchecked")
    public <T> T getVariableByKey(String key) {
        return (T) variables.get(key);
    }

    public void setResult(String key, Object value) {
        results.put(key, value);
    }

    @SuppressWarnings("unchecked")
    public <T> T getResult(String key) {
        return (T) results.get(key);
    }


    public String getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }

    public String getCurrentStageId() {
        return currentStageId;
    }

    public void setCurrentStageId(String currentStageId) {
        this.currentStageId = currentStageId;
    }

    public String getCurrentUnitId() {
        return currentUnitId;
    }

    public void setCurrentUnitId(String currentUnitId) {
        this.currentUnitId = currentUnitId;
    }


    public Map<String, Object> getResults() {
        return results;
    }

    @SuppressWarnings("unchecked")
    public <T> T getDebugInfoByKey(String key) {
        return (T) debugInfo.get(key);
    }

    public void setDebugInfo(String key, Object value) {
        debugInfo.put(key, value);
    }

    public Map<String, Object> getDebugInfo() {
        return debugInfo;
    }

    public boolean isDebugMode() {
        return debugMode;
    }

    public void setDebugMode(boolean debugMode) {
        this.debugMode = debugMode;
    }
}