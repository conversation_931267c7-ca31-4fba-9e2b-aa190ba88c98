package com.qunar.strategy.framework.common.function.providers;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.qunar.strategy.framework.common.constants.ContextVarConst;
import com.qunar.strategy.framework.common.function.CustomFunctionProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * ABTestInExpFunction的SPI提供者
 */
public class ABTestInExpFunctionProvider implements CustomFunctionProvider {
    
    @Override
    public AbstractFunction getFunction() {
        return new ABTestInExpFunction();
    }
}

/**
 * ABTestInExpFunction 判断是否在实验中 注意第一个参数代表id，第二个参数代表期望值
 */
class ABTestInExpFunction extends AbstractFunction {

    private static final Logger log = LoggerFactory.getLogger(ABTestInExpFunction.class);

    @Override
    public String getName() {
        return "abTestInExp";
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
        // 获取 abTest 对象
        Object abTest = env.get(ContextVarConst.AB_CONTEXT_KEY);
        if (abTest == null) {
            return AviatorBoolean.valueOf(false);
        }

        // 获取配置ID
        String configId = arg1.getValue(env).toString();
        String expectedValue = arg2.getValue(env).toString();

        if (abTest instanceof Map) {
            Map<?, ?> abTestMap = (Map<?, ?>) abTest;

            // 直接使用配置ID查找分组值
            if (abTestMap.containsKey(configId)) {
                String actualValue = abTestMap.get(configId).toString();
                return AviatorBoolean.valueOf(expectedValue.equals(actualValue));
            } else {
                //意味着没有初始化，检查是否使用AbTestConfigRegistry注册了实验ID
                log.warn("abTestMap does not contain configId: {}", configId);
            }
        }

        return AviatorBoolean.valueOf(false);
    }
}
