package com.qunar.strategy.framework.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 属性访问工具类，支持通过点号分隔的路径访问嵌套属性
 * 例如："address.city" 将获取 user 对象的 address 属性的 city 属性
 */
public class PropertyAccessor {
    private static final Logger log = LoggerFactory.getLogger(PropertyAccessor.class);
    
    // 缓存类的getter方法，提高性能
    private static final ConcurrentHashMap<Class<?>, ConcurrentHashMap<String, Method>> GETTER_CACHE = 
            new ConcurrentHashMap<>();
    
    // 缓存属性路径解析结果，避免重复split操作
    private static final ConcurrentHashMap<String, String[]> PROPERTY_PATH_CACHE = 
            new ConcurrentHashMap<>();
    
    // 缓存类型+属性路径的访问器，实现最高性能
    private static final ConcurrentHashMap<Class<?>, ConcurrentHashMap<String, PropertyAccessChain>> 
            ACCESS_CHAIN_CACHE = new ConcurrentHashMap<>();
    
    /**
     * 属性访问链，预先构建好的属性访问序列
     */
    private static class PropertyAccessChain {
        private final PropertyAccessStep[] steps;
        
        public PropertyAccessChain(PropertyAccessStep[] steps) {
            this.steps = steps;
        }
        
        /**
         * 执行属性访问链
         * @param root 根对象
         * @return 属性值
         */
        public Object access(Object root) {
            Object current = root;
            for (PropertyAccessStep step : steps) {
                if (current == null) {
                    return null;
                }
                current = step.access(current);
            }
            return current;
        }
    }
    
    /**
     * 属性访问步骤，表示单个属性访问操作
     */
    private interface PropertyAccessStep {
        /**
         * 访问对象的属性
         * @param obj 目标对象
         * @return 属性值
         */
        Object access(Object obj);
    }
    
    /**
     * 基于Method的属性访问步骤
     */
    private static class MethodPropertyAccessStep implements PropertyAccessStep {
        private final Method method;
        
        public MethodPropertyAccessStep(Method method) {
            this.method = method;
            // 设置方法可访问，绕过访问控制检查
            if (!method.isAccessible()) {
                method.setAccessible(true);
            }
        }
        
        @Override
        public Object access(Object obj) {
            try {
                return method.invoke(obj);
            } catch (Exception e) {
                log.debug("Failed to invoke method: {}", method.getName(), e);
                return null;
            }
        }
    }
    
    /**
     * 基于Map的属性访问步骤
     */
    private static class MapPropertyAccessStep implements PropertyAccessStep {
        private final String key;
        
        public MapPropertyAccessStep(String key) {
            this.key = key;
        }
        
        @Override
        public Object access(Object obj) {
            if (obj instanceof Map) {
                return ((Map<?, ?>) obj).get(key);
            }
            return null;
        }
    }
    
    /**
     * 获取对象的属性值，支持嵌套属性，使用高性能缓存
     * 
     * @param obj 目标对象
     * @param propertyPath 属性路径，例如 "address.city"
     * @return 属性值，如果任何中间属性为null或属性不存在，则返回null
     */
    public static Object getProperty(Object obj, String propertyPath) {
        if (obj == null || propertyPath == null || propertyPath.isEmpty()) {
            return null;
        }
        
        // 尝试使用预构建的访问链
        Class<?> objClass = obj.getClass();
        ConcurrentHashMap<String, PropertyAccessChain> classChains = 
                ACCESS_CHAIN_CACHE.get(objClass);
        
        if (classChains != null) {
            PropertyAccessChain chain = classChains.get(propertyPath);
            if (chain != null) {
                return chain.access(obj);
            }
        }
        
        // 从缓存获取已解析的属性路径
        String[] properties = PROPERTY_PATH_CACHE.computeIfAbsent(
                propertyPath, path -> path.split("\\."));
        
        // 使用传统方式获取属性值
        Object result = getPropertyTraditional(obj, properties);
        
        // 构建并缓存访问链，以便下次直接使用, 如果为null可能就没有访问到最深层，所以就不用构建访问链缓存了
        if (result != null) {
            buildAndCacheAccessChain(objClass, propertyPath, properties, obj);
        }
        
        return result;
    }
    
    /**
     * 使用传统方式获取属性值
     */
    private static Object getPropertyTraditional(Object obj, String[] properties) {
        Object currentObj = obj;
        
        for (String property : properties) {
            if (currentObj == null) {
                return null;
            }
            
            // 处理Map类型
            if (currentObj instanceof Map) {
                currentObj = ((Map<?, ?>) currentObj).get(property);
                continue;
            }
            
            // 使用getter方法获取属性值
            Method getter = getGetterMethod(currentObj.getClass(), property);
            if (getter == null) {
                log.error("No getter method found for property '{}' in class {}",
                        property, currentObj.getClass().getName());
                return null;
            }
            
            try {
                currentObj = getter.invoke(currentObj);
            } catch (Exception e) {
                log.error("Failed to get property '{}' from object of class {}",
                        property, currentObj.getClass().getName(), e);
                return null;
            }
        }
        
        return currentObj;
    }
    
    /**
     * 构建并缓存访问链
     */
    private static void buildAndCacheAccessChain(Class<?> rootClass, String propertyPath, 
                                                String[] properties, Object rootObj) {
        try {
            // 构建访问步骤
            PropertyAccessStep[] steps = new PropertyAccessStep[properties.length];
            Object currentObj = rootObj;
            
            for (int i = 0; i < properties.length; i++) {
                String property = properties[i];
                
                if (currentObj instanceof Map) {
                    steps[i] = new MapPropertyAccessStep(property);
                    currentObj = ((Map<?, ?>) currentObj).get(property);
                } else {
                    Method getter = getGetterMethod(currentObj.getClass(), property);
                    if (getter == null) {
                        return; // 无法构建完整的访问链
                    }
                    steps[i] = new MethodPropertyAccessStep(getter);
                    currentObj = getter.invoke(currentObj);
                }
                
                if (currentObj == null) {
                    return; // 中间值为null，无法继续构建
                }
            }
            
            // 创建并缓存访问链
            PropertyAccessChain chain = new PropertyAccessChain(steps);
            ConcurrentHashMap<String, PropertyAccessChain> classChains = 
                    ACCESS_CHAIN_CACHE.computeIfAbsent(rootClass, k -> new ConcurrentHashMap<>());
            classChains.put(propertyPath, chain);
            
        } catch (Exception e) {
            log.error("Failed to build access chain for path: {}", propertyPath, e);
        }
    }
    
    /**
     * 获取指定类型指定属性的getter方法
     */
    private static Method getGetterMethod(Class<?> clazz, String property) {
        // 从缓存中获取该类的所有getter方法
        ConcurrentHashMap<String, Method> getters = GETTER_CACHE.computeIfAbsent(
                clazz, k -> new ConcurrentHashMap<>());
        
        // 从缓存中获取指定属性的getter方法
        return getters.computeIfAbsent(property, p -> {
            // 构造可能的getter方法名
            String capitalizedProperty = p.substring(0, 1).toUpperCase() + p.substring(1);
            String getterName = "get" + capitalizedProperty;
            String isGetterName = "is" + capitalizedProperty;
            
            // 尝试获取标准getter方法
            try {
                Method method = clazz.getMethod(getterName);
                // 设置方法可访问，绕过访问控制检查
                method.setAccessible(true);
                return method;
            } catch (NoSuchMethodException e) {
                // 尝试获取boolean类型的is方法
                try {
                    Method isMethod = clazz.getMethod(isGetterName);
                    if (isMethod.getReturnType() == boolean.class || 
                            isMethod.getReturnType() == Boolean.class) {
                        isMethod.setAccessible(true);
                        return isMethod;
                    }
                } catch (NoSuchMethodException ex) {
                    // 忽略
                }
                
                // 尝试直接获取字段方法（有些类可能直接暴露字段作为方法）
                try {
                    Method fieldMethod = clazz.getMethod(p);
                    fieldMethod.setAccessible(true);
                    return fieldMethod;
                } catch (NoSuchMethodException ex) {
                    // 忽略
                }
                
                return null;
            }
        });
    }
    
    /**
     * 获取对象的属性值并转换为指定类型
     * 
     * @param obj 目标对象
     * @param propertyPath 属性路径，例如 "address.city"
     * @param targetType 目标类型
     * @param <T> 目标类型
     * @return 转换后的属性值，如果转换失败则返回null
     */
    @SuppressWarnings("unchecked")
    public static <T> T getProperty(Object obj, String propertyPath, Class<T> targetType) {
        Object value = getProperty(obj, propertyPath);
        if (value == null) {
            return null;
        }
        
        // 如果值已经是目标类型，直接返回
        if (targetType.isInstance(value)) {
            return (T) value;
        }
        
        // 尝试类型转换
        try {
            if (targetType == String.class) {
                return (T) value.toString();
            } else if (targetType == Integer.class || targetType == int.class) {
                if (value instanceof Number) {
                    return (T) Integer.valueOf(((Number) value).intValue());
                } else {
                    return (T) Integer.valueOf(value.toString());
                }
            } else if (targetType == Long.class || targetType == long.class) {
                if (value instanceof Number) {
                    return (T) Long.valueOf(((Number) value).longValue());
                } else {
                    return (T) Long.valueOf(value.toString());
                }
            } else if (targetType == Double.class || targetType == double.class) {
                if (value instanceof Number) {
                    return (T) Double.valueOf(((Number) value).doubleValue());
                } else {
                    return (T) Double.valueOf(value.toString());
                }
            } else if (targetType == Boolean.class || targetType == boolean.class) {
                if (value instanceof Boolean) {
                    return (T) value;
                } else {
                    return (T) Boolean.valueOf(value.toString());
                }
            }
        } catch (Exception e) {
            log.debug("Failed to convert value '{}' to type {}", value, targetType.getName(), e);
            return null;
        }
        
        log.debug("Unsupported target type: {}", targetType.getName());
        return null;
    }
}