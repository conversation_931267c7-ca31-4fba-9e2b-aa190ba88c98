package com.qunar.strategy.framework.common.model;

import java.util.Objects;

public class StrategyRouteRule {
    /**
     * 策略ID
     */
    private String strategyId;
    
    /**
     * 配置文件名
     */
    private String configFileName;
    
    /**
     * 当前活跃版本
     */
    private String activeVersion;
    
    /**
     * 触发条件表达式
     */
    private String condition;

    /*
     * 是否启用
     */
    private Boolean enable = true;
    
    /**
     * 优先级，数字越小优先级越高
     */
    private int priority;

    public String getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }

    public String getConfigFileName() {
        return configFileName;
    }

    public void setConfigFileName(String configFileName) {
        this.configFileName = configFileName;
    }

    public String getActiveVersion() {
        return activeVersion;
    }

    public void setActiveVersion(String activeVersion) {
        this.activeVersion = activeVersion;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        StrategyRouteRule that = (StrategyRouteRule) o;
        return priority == that.priority
                && Objects.equals(strategyId, that.strategyId)
                && Objects.equals(configFileName, that.configFileName)
                && Objects.equals(activeVersion, that.activeVersion)
                && Objects.equals(condition, that.condition)
                && Objects.equals(enable, that.enable);
    }

    @Override
    public int hashCode() {
        return Objects.hash(strategyId, configFileName, activeVersion, condition, enable, priority);
    }
}