package com.qunar.strategy.framework.core.engine;

import com.qunar.hotel.qmonitor.QMonitor;
import com.qunar.strategy.framework.common.model.*;
import com.qunar.strategy.framework.core.config.StrategyRouteManager;
import com.qunar.strategy.framework.core.executor.ExecutorRepository;
import com.qunar.strategy.framework.core.executor.UnitExecutor;
import com.qunar.strategy.framework.common.util.ExpressionEvaluator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class StrategyEngine {
    private final static Logger log = LoggerFactory.getLogger(StrategyEngine.class);
    private final ExecutorRepository executorRepository;
    private final StrategyRouteManager strategyRouteManager;
    private final ExpressionEvaluator expressionEvaluator;

    public StrategyEngine(ExecutorRepository executorRepository, StrategyRouteManager strategyRouteManager) {
        this.executorRepository = executorRepository;
        this.strategyRouteManager = strategyRouteManager;
        this.expressionEvaluator = ExpressionEvaluator.getInstance();
    }

    /**
     * 执行策略
     * @param context 上下文参数
     */
    public void execute(ExecutionContext context) {
        // 获取匹配的策略规则
        StrategyRouteRule matchingRule = strategyRouteManager.getMatchingRules(context);

        if (matchingRule == null) {
            log.debug("No matching strategy rules found for context");
            return;
        }
        String strategyId = matchingRule.getStrategyId();

        log.debug("Selected strategy: {} with priority: {}", strategyId, matchingRule.getPriority());
        // 执行匹配的策略
        executeStrategy(strategyId, context);
    }

    /**
     * 执行指定ID的策略
     * @param strategyId 策略ID
     * @param context 上下文参数
     */
    public void executeStrategy(String strategyId, ExecutionContext context) {
        long startStrategy = System.currentTimeMillis();

        Strategy strategy = strategyRouteManager.getStrategy(strategyId);

        if (strategy == null) {
            log.error("Strategy config not found for strategy id: {}", strategyId);
            return;
        }
        context.setStrategyId(strategyId);
        log.debug("Executing strategy: {}", strategy.getStrategyName());

        // 执行策略中的所有阶段
        for (Stage stage : strategy.getStages()) {
            context.setCurrentStageId(strategyId);
            if (expressionEvaluator.evaluateCondition(stage.getCondition(), context)) {
                log.debug("Executing stage: {}", stage.getStageName());
                executeStage(stage, context);
            } else {
                log.debug("Skipping stage: {} due to condition not met", stage.getStageName());
            }
        }
        QMonitor.recordOne("strategy_execute_" + strategy.getStrategyId(), System.currentTimeMillis() - startStrategy);
    }

    /**
     * 执行策略阶段
     * @param stage 策略阶段
     * @param context 上下文参数
     */
    private void executeStage(Stage stage, ExecutionContext context) {
        // 使用互斥组执行器执行阶段中的所有执行单元
        long startStage = System.currentTimeMillis();
        context.setCurrentStageId(stage.getStageId());

        log.debug("Executing stage: {} with {} execution units", stage.getStageName(), stage.getExecutionUnits().size());

        // 使用预处理的执行计划
        if (stage.getExecutionPlan() != null) {
            executeWithPlan(stage.getExecutionPlan(), context);
        } else {
            log.error("No execution plan found for stage: {}, skipping", stage.getStageName());
            QMonitor.recordOne("no_execution_plan_found");
        }
        QMonitor.recordOne("stage_execute_" + stage.getStageId(), System.currentTimeMillis() - startStage);
    }

    /** 使用预处理的执行计划执行
     * @param executionPlan 预处理的执行计划
     * @param context 执行上下文
     */
    public void executeWithPlan(ExecutionPlan executionPlan, ExecutionContext context) {
        if (executionPlan == null || executionPlan.getExecutionItems() == null) {
            return;
        }
        log.debug("Executing plan with {} items", executionPlan.getExecutionItems().size());
        // 按预处理的顺序执行
        for (ExecutionPlan.ExecutionItem item : executionPlan.getExecutionItems()) {
            ExecutionUnit unitToExecute = item.getExecutableUnit(context);
            if (unitToExecute != null) {
                executeUnit(unitToExecute, context);
            } else {
                log.error("No executable unit found for {}: {}", item.getItemType(), item.getItemId());
                QMonitor.recordOne("no_executable_unit_found");
            }
        }
    }

    /**
     * 执行单个执行单元
     */
    private void executeUnit(ExecutionUnit unit, ExecutionContext context) {
        long startUnit = System.currentTimeMillis();
        try {
            String executorClass = unit.getComponentName();
            UnitExecutor executor = executorRepository.getExecutor(executorClass);
            if (executor == null) {
                log.error("Executor not found for class: {}", executorClass);
                QMonitor.recordOne("no_executor_found");
                return;
            }
            context.setCurrentUnitId(unit.getUnitId());
            // 执行单元
            executor.execute(context, unit);
            log.debug("Successfully executed unit: {}", unit.getUnitName());
        } catch (Exception e) {
            log.error("Failed to execute unit: {}", unit.getUnitName(), e);
            QMonitor.recordOne("unit_execute_failed");
        } finally {
            QMonitor.recordOne("unit_execute_" + unit.getUnitId(), System.currentTimeMillis() - startUnit);
        }
    }
}