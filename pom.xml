<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>qunar-supom-generic</artifactId>
        <groupId>qunar.common</groupId>
        <version>2.0.4</version>
    </parent>

    <groupId>com.qunar.hotel</groupId>
    <artifactId>strategy-engine</artifactId>
    <version>1.0.1</version>
    <packaging>pom</packaging>


    <modules>
        <module>strategy-common</module>
        <module>strategy-core</module>
        <module>strategy-recall</module>
        <module>strategy-filter</module>
        <module>strategy-context</module>
        <module>strategy-engine-spring-boot-starter</module>
        <module>strategy-rerank</module>
        <module>strategy-visual-config</module>
    </modules>

    <name>strategy-engine</name>
    <properties>
        <java.version>1.8</java.version>
        <strategy-engine.version>${project.version}</strategy-engine.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <!-- Framework Version -->


        <!-- Dependencies Versions -->
        <spring-boot.version>2.5.5</spring-boot.version>
        <lombok.version>1.18.30</lombok.version>
        <slf4j.version>2.0.12</slf4j.version>
        <commons-lang.version>2.5</commons-lang.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <com.fasterxml.jackson.version>2.9.8</com.fasterxml.jackson.version>
        <qconfig-client>2.0.12</qconfig-client>
        <qunar.common>11.1.3</qunar.common>
        <qunar.tcdev.version>5.1.8</qunar.tcdev.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot Dependencies -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Third-party Dependencies -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <!-- 统一管理 Jackson 依赖版本 -->
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${com.fasterxml.jackson.version}</version> <!-- 使用你项目需要的版本 -->
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--<dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${com.fasterxml.jackson.version}</version>
            </dependency>-->

            <!-- Project Internal Dependencies -->
            <dependency>
                <groupId>com.qunar.hotel</groupId>
                <artifactId>strategy-recall</artifactId>
                <version>${strategy-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunar.hotel</groupId>
                <artifactId>strategy-common</artifactId>
                <version>${strategy-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunar.hotel</groupId>
                <artifactId>strategy-core</artifactId>
                <version>${strategy-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunar.hotel</groupId>
                <artifactId>strategy-context</artifactId>
                <version>${strategy-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunar.hotel</groupId>
                <artifactId>strategy-filter</artifactId>
                <version>${strategy-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qunar.hotel</groupId>
                <artifactId>strategy-rerank</artifactId>
                <version>${strategy-engine.version}</version>
            </dependency>

            <!-- Project Internal Dependencies -->
           <!-- <dependency>
                <groupId>qunar.tc.qconfig</groupId>
                <artifactId>qconfig-client</artifactId>
                <version>${qconfig-client}</version>
            </dependency>-->

            <dependency>
                <groupId>qunar.tc</groupId>
                <artifactId>tcdev</artifactId>
                <version>${qunar.tcdev.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--<dependency>
                <groupId>qunar.common</groupId>
                <artifactId>common-core</artifactId>
                <version>${qunar.common}</version>
            </dependency>
            <dependency>
                <groupId>qunar.common</groupId>
                <artifactId>common-http</artifactId>
                <version>${qunar.common}</version>
            </dependency>-->

            <dependency>
                <groupId>com.qunar.datateam.abtest</groupId>
                <artifactId>qabtest-client</artifactId>
                <version>1.2.0</version>
                <exclusions>
                    <!-- auto-generated by tcdev pom auto upgrader -->
                    <exclusion>
                        <groupId>com.thoughtworks.xstream</groupId>
                        <artifactId>xstream</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qunar.tc</groupId>
                <artifactId>cbrs-over-hqmonitor</artifactId>
                <version>1.1.5</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>5.3.3</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <build>
        <pluginManagement>

            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>1.0.1</version>
                    <executions>
                        <execution>
                            <id>default-cli</id>
                            <goals>
                                <goal>enforce</goal>
                            </goals>
                            <configuration>
                                <rules>
                                    <banDuplicateClasses>
                                        <ignoreClasses>
                                            <ignoreClass>module-info</ignoreClass>
                                        </ignoreClasses>
                                    </banDuplicateClasses>
                                </rules>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.6.3</version>
                    <executions>
                        <execution>
                            <id>attach-javadocs</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
