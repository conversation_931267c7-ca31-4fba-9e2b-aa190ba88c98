# 策略引擎可视化配置系统

## 概述

策略引擎可视化配置系统是一个基于Web的图形化界面，用于设计、配置和管理策略引擎的策略规则。通过拖拽式的DAG（有向无环图）编辑器，用户可以直观地构建复杂的策略流程，并自动生成相应的JSON配置文件。

## 主要功能

### 1. 可视化策略设计
- **DAG编辑器**：基于G6图形库的拖拽式策略设计界面
- **节点类型**：支持开始节点、结束节点、阶段节点、执行单元节点、条件节点、互斥组节点
- **连接关系**：通过连线定义节点间的执行顺序和依赖关系

### 2. 智能表达式编辑
- **语法验证**：实时验证条件表达式的语法正确性
- **智能提示**：提供变量和函数的自动补全建议
- **语义检查**：检查变量和函数的使用是否合理
- **性能建议**：提供表达式优化建议

### 3. 执行器管理
- **自动发现**：自动扫描和发现系统中的执行器
- **分类展示**：按功能分类展示执行器（召回、重排、过滤等）
- **配置参数**：为每个执行器提供可配置的参数界面
- **拖拽创建**：直接拖拽执行器创建执行单元节点

### 4. 配置生成
- **策略配置**：自动生成符合策略引擎格式的JSON配置
- **路由配置**：生成routes.json路由规则配置
- **实时预览**：实时预览生成的配置文件
- **验证检查**：生成前进行完整性和正确性验证

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   策略引擎      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ DAG编辑器   │ │◄──►│ │ 配置生成器  │ │◄──►│ │ 策略执行器  │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 表达式编辑器│ │◄──►│ │ 表达式验证器│ │◄──►│ │ 表达式引擎  │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 执行器面板  │ │◄──►│ │ 执行器发现  │ │◄──►│ │ 执行器仓库  │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 快速开始

### 1. 启动应用

```bash
cd strategy-visual-config
mvn spring-boot:run
```

应用启动后，访问 http://localhost:8081 即可打开可视化配置界面。

### 2. 创建策略

1. **新建策略**：点击"新建"按钮，输入策略名称
2. **添加节点**：从左侧面板拖拽节点到画布
3. **连接节点**：通过连线定义执行顺序
4. **配置属性**：选择节点后在右侧面板配置属性
5. **验证策略**：点击"验证"按钮检查策略正确性
6. **生成配置**：点击"生成配置"按钮生成JSON文件

### 3. 节点类型说明

#### 开始节点
- **用途**：标记策略执行的起点
- **属性**：仅包含基本信息（ID、名称、描述）

#### 阶段节点
- **用途**：将执行单元分组到不同阶段
- **属性**：阶段ID、条件表达式、执行顺序
- **示例**：召回阶段、重排阶段、过滤阶段

#### 执行单元节点
- **用途**：具体的业务逻辑执行单元
- **属性**：执行器类、条件表达式、互斥组、优先级、配置参数
- **示例**：Redis召回、收益重排、价格过滤

#### 条件节点
- **用途**：根据条件决定执行路径
- **属性**：条件表达式
- **示例**：用户类型判断、时间段判断

#### 互斥组节点
- **用途**：管理互斥执行的单元组
- **属性**：组名称、优先级规则
- **示例**：AB测试分组

#### 结束节点
- **用途**：标记策略执行的终点
- **属性**：仅包含基本信息

## 表达式语法

### 基本语法
```javascript
// 变量访问
source == 'mini'
isWeekend == true
price > 100

// 对象属性访问
abtest.exp1 == 'A'
hotel.city == 'Beijing'

// 函数调用
hasIdentity('newUser')
hasUserTag('vipUser')
abTestInExp('exp1', 'A')
isInCity('Beijing', targetCity)
```

### 常用变量
- `userId`: 用户ID
- `source`: 来源渠道
- `isWeekend`: 是否周末
- `targetCity`: 目标城市
- `price`: 价格
- `level`: 等级
- `abtest`: AB测试分组
- `userTags`: 用户标签

### 常用函数
- `hasIdentity(tag)`: 检查用户身份
- `hasUserTag(tag)`: 检查用户标签
- `abTestInExp(expId, slotId)`: 检查AB测试分组
- `isInCity(city1, city2)`: 检查城市匹配
- `include(collection, item)`: 检查集合包含
- `isEmpty(value)`: 检查是否为空

## API接口

### 执行器相关
- `GET /api/visual-config/executors` - 获取所有执行器
- `GET /api/visual-config/executors/{name}` - 获取执行器详情

### 验证相关
- `POST /api/visual-config/validate-dag` - 验证DAG
- `POST /api/visual-config/validate-expression` - 验证表达式

### 配置生成
- `POST /api/visual-config/generate-config` - 生成完整配置
- `POST /api/visual-config/preview-strategy` - 预览策略配置
- `POST /api/visual-config/preview-routes` - 预览路由配置

### 策略管理
- `POST /api/visual-config/save-dag` - 保存策略DAG
- `GET /api/visual-config/load-dag/{id}` - 加载策略DAG
- `GET /api/visual-config/strategies` - 获取策略列表

## 配置示例

### 生成的策略配置
```json
{
  "strategyId": "profit-rerank-strategy",
  "strategyName": "收益重排策略",
  "description": "基于收益的酒店重排策略",
  "stages": [
    {
      "stageId": "rerank_stage",
      "stageName": "重排阶段",
      "condition": "!#context.results.isEmpty()",
      "executionUnits": [
        {
          "unitId": "profit_rerank_unit",
          "unitName": "收益重排",
          "executorClass": "profitRatioExecutor",
          "condition": "#vars[abTest][profit_exp] == 'A'",
          "configuration": {
            "startPosition": 1,
            "endPosition": 100,
            "commissionWeight": 1.0
          }
        }
      ]
    }
  ]
}
```

### 生成的路由配置
```json
{
  "profit-rerank-strategy": {
    "configFileName": "profit-rerank-strategy.json",
    "activeVersion": "1640995200000",
    "condition": "source == 'mini' && isWeekend == true",
    "priority": 0
  }
}
```

## 扩展开发

### 添加新的执行器
1. 实现 `UnitExecutor` 接口
2. 使用 `@UnitExecutorComponent` 注解
3. 重启应用，执行器会自动被发现

### 添加新的节点类型
1. 继承 `VisualNode` 类
2. 在前端添加对应的节点样式和属性面板
3. 在配置生成器中添加转换逻辑

### 自定义表达式函数
1. 在 `ExpressionEvaluator` 中注册新函数
2. 在表达式验证服务中添加验证逻辑
3. 在前端添加函数提示

## 注意事项

1. **浏览器兼容性**：建议使用Chrome、Firefox、Safari等现代浏览器
2. **性能考虑**：大型策略图可能影响渲染性能，建议合理拆分
3. **表达式复杂度**：避免过于复杂的表达式，影响执行性能
4. **节点命名**：使用有意义的节点名称，便于维护

## 故障排除

### 常见问题
1. **执行器不显示**：检查执行器是否正确注册，查看启动日志
2. **表达式验证失败**：检查语法和变量名是否正确
3. **配置生成失败**：检查DAG结构是否完整，节点属性是否配置正确
4. **页面加载缓慢**：检查网络连接，清除浏览器缓存

### 日志查看
```bash
# 查看应用日志
tail -f logs/strategy-visual-config.log

# 查看特定组件日志
grep "ExecutorDiscoveryService" logs/strategy-visual-config.log
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的DAG编辑功能
- 实现表达式验证和智能提示
- 支持策略和路由配置生成
