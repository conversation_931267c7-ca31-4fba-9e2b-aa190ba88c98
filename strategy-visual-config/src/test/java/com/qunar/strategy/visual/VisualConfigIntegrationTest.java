package com.qunar.strategy.visual;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qunar.strategy.visual.model.*;
import com.qunar.strategy.visual.service.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 可视化配置系统集成测试
 */
@SpringBootTest
@AutoConfigureTestMvc
public class VisualConfigIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private ExecutorDiscoveryService executorDiscoveryService;
    
    @Autowired
    private DAGValidationService dagValidationService;
    
    @Autowired
    private ConfigGenerationService configGenerationService;
    
    @Autowired
    private ExpressionValidationService expressionValidationService;
    
    @Test
    public void testGetExecutors() throws Exception {
        mockMvc.perform(get("/api/visual-config/executors"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }
    
    @Test
    public void testValidateExpression() throws Exception {
        Map<String, String> request = new HashMap<>();
        request.put("expression", "source == 'mini' && hasIdentity('newUser')");
        
        mockMvc.perform(post("/api/visual-config/validate-expression")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpected(status().isOk())
                .andExpect(jsonPath("$.valid").value(true));
    }
    
    @Test
    public void testValidateDAG() throws Exception {
        StrategyDAG dag = createTestDAG();
        
        mockMvc.perform(post("/api/visual-config/validate-dag")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dag)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.valid").exists());
    }
    
    @Test
    public void testGenerateConfig() throws Exception {
        StrategyDAG dag = createTestDAG();
        Map<String, Object> request = new HashMap<>();
        request.put("dag", dag);
        request.put("routeConfigs", new ArrayList<>());
        
        mockMvc.perform(post("/api/visual-config/generate-config")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").exists());
    }
    
    @Test
    public void testExecutorDiscoveryService() {
        List<ExecutorDiscoveryService.ExecutorMetadata> executors = executorDiscoveryService.getAllExecutors();
        assertNotNull(executors);
        assertFalse(executors.isEmpty());
        
        // 验证执行器元数据
        for (ExecutorDiscoveryService.ExecutorMetadata executor : executors) {
            assertNotNull(executor.getName());
            assertNotNull(executor.getClassName());
            assertNotNull(executor.getCategory());
        }
    }
    
    @Test
    public void testDAGValidation() {
        // 测试有效的DAG
        StrategyDAG validDAG = createTestDAG();
        DAGValidationService.ValidationResult result = dagValidationService.validateDAG(validDAG);
        assertTrue(result.isValid());
        
        // 测试无效的DAG
        StrategyDAG invalidDAG = new StrategyDAG();
        result = dagValidationService.validateDAG(invalidDAG);
        assertFalse(result.isValid());
        assertFalse(result.getErrors().isEmpty());
    }
    
    @Test
    public void testExpressionValidation() {
        // 测试有效表达式
        ExpressionValidationService.ValidationResult result = 
                expressionValidationService.validateExpression("source == 'mini'");
        assertTrue(result.isValid());
        
        // 测试无效表达式
        result = expressionValidationService.validateExpression("source == ");
        assertFalse(result.isValid());
        assertNotNull(result.getErrorMessage());
        
        // 测试空表达式
        result = expressionValidationService.validateExpression("");
        assertTrue(result.isValid());
        assertFalse(result.getSuggestions().isEmpty());
    }
    
    @Test
    public void testConfigGeneration() throws Exception {
        StrategyDAG dag = createTestDAG();
        
        // 测试策略配置生成
        String strategyJson = configGenerationService.generateStrategyJson(dag);
        assertNotNull(strategyJson);
        assertFalse(strategyJson.isEmpty());
        
        // 验证生成的JSON可以解析
        objectMapper.readTree(strategyJson);
        
        // 测试路由配置生成
        List<ConfigGenerationService.RouteRuleConfig> routeConfigs = new ArrayList<>();
        routeConfigs.add(new ConfigGenerationService.RouteRuleConfig(
                dag.getStrategyId(), 
                dag.getStrategyId() + ".json", 
                "true", 
                0
        ));
        
        String routeJson = configGenerationService.generateRouteJson(routeConfigs);
        assertNotNull(routeJson);
        assertFalse(routeJson.isEmpty());
        
        // 验证生成的JSON可以解析
        objectMapper.readTree(routeJson);
    }
    
    @Test
    public void testExpressionSuggestions() {
        List<String> suggestions = expressionValidationService.getExpressionSuggestions("");
        assertNotNull(suggestions);
        assertFalse(suggestions.isEmpty());
        
        suggestions = expressionValidationService.getExpressionSuggestions("source");
        assertNotNull(suggestions);
        assertTrue(suggestions.stream().anyMatch(s -> s.contains("source")));
    }
    
    /**
     * 创建测试用的DAG
     */
    private StrategyDAG createTestDAG() {
        StrategyDAG dag = new StrategyDAG("test-strategy", "测试策略");
        dag.setDescription("这是一个测试策略");
        
        List<VisualNode> nodes = new ArrayList<>();
        List<VisualEdge> edges = new ArrayList<>();
        
        // 创建开始节点
        StartNode startNode = new StartNode("start_1", "开始");
        startNode.setPosition(new VisualNode.Position(100, 100));
        nodes.add(startNode);
        
        // 创建阶段节点
        StageNode stageNode = new StageNode("stage_1", "重排阶段");
        stageNode.setStageId("rerank_stage");
        stageNode.setCondition("!#context.results.isEmpty()");
        stageNode.setOrder(1);
        stageNode.setPosition(new VisualNode.Position(300, 100));
        nodes.add(stageNode);
        
        // 创建执行单元节点
        ExecutionUnitNode unitNode = new ExecutionUnitNode("unit_1", "收益重排");
        unitNode.setExecutorClass("profitRatioExecutor");
        unitNode.setCondition("#vars[abTest][profit_exp] == 'A'");
        unitNode.setOrder(1);
        unitNode.setPriority(1);
        
        Map<String, Object> config = new HashMap<>();
        config.put("startPosition", 1);
        config.put("endPosition", 100);
        config.put("commissionWeight", 1.0);
        unitNode.setConfig(config);
        unitNode.setPosition(new VisualNode.Position(500, 100));
        nodes.add(unitNode);
        
        // 创建结束节点
        EndNode endNode = new EndNode("end_1", "结束");
        endNode.setPosition(new VisualNode.Position(700, 100));
        nodes.add(endNode);
        
        // 创建连接边
        edges.add(new VisualEdge("edge_1", "start_1", "stage_1"));
        edges.add(new VisualEdge("edge_2", "stage_1", "unit_1"));
        edges.add(new VisualEdge("edge_3", "unit_1", "end_1"));
        
        dag.setNodes(nodes);
        dag.setEdges(edges);
        
        return dag;
    }
}
