package com.qunar.strategy.visual.controller;

import com.qunar.strategy.visual.model.StrategyDAG;
import com.qunar.strategy.visual.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 可视化配置控制器
 */
@RestController
@RequestMapping("/api/visual-config")
@CrossOrigin(origins = "*")
public class VisualConfigController {
    
    @Autowired
    private ExecutorDiscoveryService executorDiscoveryService;
    
    @Autowired
    private DAGValidationService dagValidationService;
    
    @Autowired
    private ConfigGenerationService configGenerationService;
    
    @Autowired
    private ExpressionValidationService expressionValidationService;
    
    /**
     * 获取所有可用的执行器
     */
    @GetMapping("/executors")
    public ResponseEntity<List<ExecutorDiscoveryService.ExecutorMetadata>> getAllExecutors() {
        List<ExecutorDiscoveryService.ExecutorMetadata> executors = executorDiscoveryService.getAllExecutors();
        return ResponseEntity.ok(executors);
    }
    
    /**
     * 获取指定执行器的元数据
     */
    @GetMapping("/executors/{executorName}")
    public ResponseEntity<ExecutorDiscoveryService.ExecutorMetadata> getExecutorMetadata(
            @PathVariable String executorName) {
        ExecutorDiscoveryService.ExecutorMetadata metadata = executorDiscoveryService.getExecutorMetadata(executorName);
        if (metadata == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(metadata);
    }
    
    /**
     * 验证策略DAG
     */
    @PostMapping("/validate-dag")
    public ResponseEntity<DAGValidationService.ValidationResult> validateDAG(
            @RequestBody StrategyDAG dag) {
        DAGValidationService.ValidationResult result = dagValidationService.validateDAG(dag);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 验证表达式
     */
    @PostMapping("/validate-expression")
    public ResponseEntity<ExpressionValidationService.ValidationResult> validateExpression(
            @RequestBody Map<String, String> request) {
        String expression = request.get("expression");
        ExpressionValidationService.ValidationResult result = 
                expressionValidationService.validateExpression(expression);
        return ResponseEntity.ok(result);
    }
    
    /**
     * 获取表达式建议
     */
    @GetMapping("/expression-suggestions")
    public ResponseEntity<List<String>> getExpressionSuggestions(
            @RequestParam(required = false) String partial) {
        List<String> suggestions = expressionValidationService.getExpressionSuggestions(partial);
        return ResponseEntity.ok(suggestions);
    }
    
    /**
     * 生成策略配置
     */
    @PostMapping("/generate-config")
    public ResponseEntity<ConfigGenerationService.GenerationResult> generateConfig(
            @RequestBody GenerateConfigRequest request) {
        ConfigGenerationService.GenerationResult result = 
                configGenerationService.generateConfigs(request.getDag(), request.getRouteConfigs());
        return ResponseEntity.ok(result);
    }
    
    /**
     * 预览策略配置
     */
    @PostMapping("/preview-strategy")
    public ResponseEntity<String> previewStrategy(@RequestBody StrategyDAG dag) {
        try {
            String strategyJson = configGenerationService.generateStrategyJson(dag);
            return ResponseEntity.ok(strategyJson);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("生成配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 预览路由配置
     */
    @PostMapping("/preview-routes")
    public ResponseEntity<String> previewRoutes(
            @RequestBody List<ConfigGenerationService.RouteRuleConfig> routeConfigs) {
        try {
            String routeJson = configGenerationService.generateRouteJson(routeConfigs);
            return ResponseEntity.ok(routeJson);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("生成路由配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存策略DAG
     */
    @PostMapping("/save-dag")
    public ResponseEntity<Map<String, Object>> saveDAG(@RequestBody StrategyDAG dag) {
        // 这里可以实现保存到数据库或文件系统的逻辑
        // 目前返回成功响应
        return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "策略DAG保存成功",
                "strategyId", dag.getStrategyId(),
                "timestamp", System.currentTimeMillis()
        ));
    }
    
    /**
     * 加载策略DAG
     */
    @GetMapping("/load-dag/{strategyId}")
    public ResponseEntity<StrategyDAG> loadDAG(@PathVariable String strategyId) {
        // 这里可以实现从数据库或文件系统加载的逻辑
        // 目前返回空的DAG
        StrategyDAG dag = new StrategyDAG(strategyId, "示例策略");
        return ResponseEntity.ok(dag);
    }
    
    /**
     * 获取策略列表
     */
    @GetMapping("/strategies")
    public ResponseEntity<List<Map<String, Object>>> getStrategies() {
        // 这里可以实现获取所有策略列表的逻辑
        List<Map<String, Object>> strategies = List.of(
                Map.of("strategyId", "strategy-1", "strategyName", "示例策略1", "description", "这是一个示例策略"),
                Map.of("strategyId", "strategy-2", "strategyName", "示例策略2", "description", "这是另一个示例策略")
        );
        return ResponseEntity.ok(strategies);
    }
    
    /**
     * 删除策略
     */
    @DeleteMapping("/strategies/{strategyId}")
    public ResponseEntity<Map<String, Object>> deleteStrategy(@PathVariable String strategyId) {
        // 这里可以实现删除策略的逻辑
        return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "策略删除成功",
                "strategyId", strategyId
        ));
    }
    
    /**
     * 复制策略
     */
    @PostMapping("/strategies/{strategyId}/copy")
    public ResponseEntity<Map<String, Object>> copyStrategy(
            @PathVariable String strategyId,
            @RequestBody Map<String, String> request) {
        String newStrategyId = request.get("newStrategyId");
        String newStrategyName = request.get("newStrategyName");
        
        // 这里可以实现复制策略的逻辑
        return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "策略复制成功",
                "originalStrategyId", strategyId,
                "newStrategyId", newStrategyId,
                "newStrategyName", newStrategyName
        ));
    }
    
    /**
     * 生成配置请求
     */
    public static class GenerateConfigRequest {
        private StrategyDAG dag;
        private List<ConfigGenerationService.RouteRuleConfig> routeConfigs;
        
        public StrategyDAG getDag() { return dag; }
        public void setDag(StrategyDAG dag) { this.dag = dag; }
        
        public List<ConfigGenerationService.RouteRuleConfig> getRouteConfigs() { return routeConfigs; }
        public void setRouteConfigs(List<ConfigGenerationService.RouteRuleConfig> routeConfigs) { 
            this.routeConfigs = routeConfigs; 
        }
    }
}
