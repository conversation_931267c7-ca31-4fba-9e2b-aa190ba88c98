package com.qunar.strategy.visual.service;

import com.qunar.strategy.framework.common.model.ExecutionContext;
import com.qunar.strategy.framework.common.util.ExpressionEvaluator;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 表达式验证服务
 * 验证条件表达式的语法和语义正确性
 */
@Service
public class ExpressionValidationService {
    
    private final ExpressionEvaluator expressionEvaluator;
    
    // 常用的上下文变量
    private static final Set<String> COMMON_VARIABLES = Set.of(
        "userId", "isWeekend", "isMini", "source", "targetCity", "price", "level",
        "abTest", "userTags", "hotel", "timestamp", "traceId", "flowType"
    );
    
    // 常用的函数
    private static final Set<String> COMMON_FUNCTIONS = Set.of(
        "hasIdentity", "hasUserTag", "abTestInExp", "isInCity", "calculateScore",
        "include", "contains", "isEmpty", "size"
    );
    
    public ExpressionValidationService() {
        this.expressionEvaluator = ExpressionEvaluator.getInstance();
    }
    
    /**
     * 验证结果
     */
    public static class ValidationResult {
        private boolean valid;
        private String errorMessage;
        private List<String> warnings;
        private List<String> suggestions;
        
        public ValidationResult() {
            this.warnings = new ArrayList<>();
            this.suggestions = new ArrayList<>();
        }
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public List<String> getWarnings() { return warnings; }
        public void setWarnings(List<String> warnings) { this.warnings = warnings; }
        
        public List<String> getSuggestions() { return suggestions; }
        public void setSuggestions(List<String> suggestions) { this.suggestions = suggestions; }
        
        public void addWarning(String warning) { this.warnings.add(warning); }
        public void addSuggestion(String suggestion) { this.suggestions.add(suggestion); }
    }
    
    /**
     * 验证表达式
     */
    public ValidationResult validateExpression(String expression) {
        ValidationResult result = new ValidationResult();
        
        if (expression == null || expression.trim().isEmpty()) {
            result.setValid(true); // 空表达式被认为是有效的（默认为true）
            result.addSuggestion("空表达式将被视为 true");
            return result;
        }
        
        try {
            // 语法验证
            validateSyntax(expression, result);
            
            // 语义验证
            validateSemantics(expression, result);
            
            // 性能建议
            providePerformanceSuggestions(expression, result);
            
            result.setValid(true);
        } catch (Exception e) {
            result.setValid(false);
            result.setErrorMessage("表达式验证失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 语法验证
     */
    private void validateSyntax(String expression, ValidationResult result) {
        try {
            // 创建测试上下文
            ExecutionContext testContext = createTestContext();
            
            // 尝试编译表达式
            expressionEvaluator.evaluateCondition(expression, testContext);
        } catch (Exception e) {
            throw new RuntimeException("语法错误: " + e.getMessage());
        }
    }
    
    /**
     * 语义验证
     */
    private void validateSemantics(String expression, ValidationResult result) {
        // 检查变量使用
        checkVariableUsage(expression, result);
        
        // 检查函数使用
        checkFunctionUsage(expression, result);
        
        // 检查逻辑结构
        checkLogicalStructure(expression, result);
    }
    
    /**
     * 检查变量使用
     */
    private void checkVariableUsage(String expression, ValidationResult result) {
        // 提取表达式中的变量
        Set<String> usedVariables = extractVariables(expression);
        
        for (String variable : usedVariables) {
            if (!COMMON_VARIABLES.contains(variable)) {
                result.addWarning("使用了不常见的变量: " + variable + "，请确保该变量在上下文中存在");
            }
        }
        
        // 检查特殊变量的使用方式
        if (expression.contains("userTags") && !expression.contains("hasUserTag") && !expression.contains("hasIdentity")) {
            result.addSuggestion("建议使用 hasUserTag() 或 hasIdentity() 函数来检查用户标签");
        }
        
        if (expression.contains("abTest") && !expression.contains("abTestInExp")) {
            result.addSuggestion("建议使用 abTestInExp() 函数来检查AB测试分组");
        }
    }
    
    /**
     * 检查函数使用
     */
    private void checkFunctionUsage(String expression, ValidationResult result) {
        // 提取表达式中的函数
        Set<String> usedFunctions = extractFunctions(expression);
        
        for (String function : usedFunctions) {
            if (!COMMON_FUNCTIONS.contains(function)) {
                result.addWarning("使用了不常见的函数: " + function + "，请确保该函数已定义");
            }
        }
        
        // 检查函数参数
        checkFunctionParameters(expression, result);
    }
    
    /**
     * 检查逻辑结构
     */
    private void checkLogicalStructure(String expression, ValidationResult result) {
        // 检查括号匹配
        if (!isParenthesesBalanced(expression)) {
            throw new RuntimeException("括号不匹配");
        }
        
        // 检查逻辑操作符
        if (expression.contains("&&") && expression.contains("||")) {
            result.addSuggestion("表达式同时包含 && 和 ||，建议使用括号明确优先级");
        }
        
        // 检查比较操作
        if (expression.matches(".*==\\s*true.*") || expression.matches(".*==\\s*false.*")) {
            result.addSuggestion("可以简化布尔值比较，如 'flag == true' 可以简化为 'flag'");
        }
    }
    
    /**
     * 性能建议
     */
    private void providePerformanceSuggestions(String expression, ValidationResult result) {
        // 检查复杂表达式
        int operatorCount = countOperators(expression);
        if (operatorCount > 10) {
            result.addSuggestion("表达式较为复杂，建议拆分为多个简单条件");
        }
        
        // 检查字符串操作
        if (expression.contains("contains") || expression.contains("indexOf")) {
            result.addSuggestion("字符串操作可能影响性能，考虑使用更高效的匹配方式");
        }
        
        // 检查正则表达式
        if (expression.contains("matches")) {
            result.addSuggestion("正则表达式匹配可能影响性能，建议预编译正则表达式");
        }
    }
    
    /**
     * 提取变量
     */
    private Set<String> extractVariables(String expression) {
        Set<String> variables = new HashSet<>();
        
        // 匹配变量模式：#vars[variable] 或 variable
        Pattern pattern = Pattern.compile("(?:#vars\\[([^\\]]+)\\]|\\b([a-zA-Z_][a-zA-Z0-9_]*))");
        Matcher matcher = pattern.matcher(expression);
        
        while (matcher.find()) {
            String var = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
            if (var != null && !COMMON_FUNCTIONS.contains(var) && !isKeyword(var)) {
                variables.add(var);
            }
        }
        
        return variables;
    }
    
    /**
     * 提取函数
     */
    private Set<String> extractFunctions(String expression) {
        Set<String> functions = new HashSet<>();
        
        // 匹配函数模式：functionName(
        Pattern pattern = Pattern.compile("([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(");
        Matcher matcher = pattern.matcher(expression);
        
        while (matcher.find()) {
            functions.add(matcher.group(1));
        }
        
        return functions;
    }
    
    /**
     * 检查函数参数
     */
    private void checkFunctionParameters(String expression, ValidationResult result) {
        // 检查常见函数的参数
        if (expression.contains("hasIdentity(") || expression.contains("hasUserTag(")) {
            Pattern pattern = Pattern.compile("has(?:Identity|UserTag)\\(\\s*'([^']*)'\\s*\\)");
            Matcher matcher = pattern.matcher(expression);
            while (matcher.find()) {
                String tag = matcher.group(1);
                if (tag.isEmpty()) {
                    result.addWarning("用户标签不能为空");
                }
            }
        }
        
        if (expression.contains("abTestInExp(")) {
            Pattern pattern = Pattern.compile("abTestInExp\\(\\s*'([^']*)'\\s*,\\s*'([^']*)'\\s*\\)");
            Matcher matcher = pattern.matcher(expression);
            while (matcher.find()) {
                String expId = matcher.group(1);
                String slotId = matcher.group(2);
                if (expId.isEmpty() || slotId.isEmpty()) {
                    result.addWarning("AB测试实验ID和分组ID不能为空");
                }
            }
        }
    }
    
    /**
     * 检查括号是否匹配
     */
    private boolean isParenthesesBalanced(String expression) {
        int count = 0;
        for (char c : expression.toCharArray()) {
            if (c == '(') {
                count++;
            } else if (c == ')') {
                count--;
                if (count < 0) {
                    return false;
                }
            }
        }
        return count == 0;
    }
    
    /**
     * 计算操作符数量
     */
    private int countOperators(String expression) {
        int count = 0;
        String[] operators = {"&&", "||", "==", "!=", ">=", "<=", ">", "<", "+", "-", "*", "/"};
        for (String op : operators) {
            count += (expression.length() - expression.replace(op, "").length()) / op.length();
        }
        return count;
    }
    
    /**
     * 判断是否为关键字
     */
    private boolean isKeyword(String word) {
        Set<String> keywords = Set.of("true", "false", "null", "and", "or", "not", "if", "else", "then");
        return keywords.contains(word.toLowerCase());
    }
    
    /**
     * 创建测试上下文
     */
    private ExecutionContext createTestContext() {
        ExecutionContext context = new ExecutionContext();
        
        // 添加常用变量
        context.setVariable("userId", "test_user");
        context.setVariable("isWeekend", true);
        context.setVariable("isMini", true);
        context.setVariable("source", "mini");
        context.setVariable("targetCity", "Beijing");
        context.setVariable("price", 500.0);
        context.setVariable("level", 4);
        
        // 添加复杂对象
        Map<String, String> abTest = new HashMap<>();
        abTest.put("exp1", "A");
        abTest.put("exp2", "B");
        context.setVariable("abTest", abTest);
        
        Set<String> userTags = new HashSet<>();
        userTags.add("newUser");
        userTags.add("vipUser");
        context.setVariable("userTags", userTags);
        
        return context;
    }
    
    /**
     * 获取表达式建议
     */
    public List<String> getExpressionSuggestions(String partialExpression) {
        List<String> suggestions = new ArrayList<>();
        
        if (partialExpression == null || partialExpression.trim().isEmpty()) {
            // 提供常用表达式模板
            suggestions.add("source == 'mini'");
            suggestions.add("isWeekend == true");
            suggestions.add("hasIdentity('newUser')");
            suggestions.add("abtest.exp1 == 'A'");
            suggestions.add("price > 100");
            return suggestions;
        }
        
        String partial = partialExpression.toLowerCase();
        
        // 变量建议
        for (String var : COMMON_VARIABLES) {
            if (var.toLowerCase().startsWith(partial)) {
                suggestions.add(var);
            }
        }
        
        // 函数建议
        for (String func : COMMON_FUNCTIONS) {
            if (func.toLowerCase().startsWith(partial)) {
                suggestions.add(func + "()");
            }
        }
        
        return suggestions;
    }
}
