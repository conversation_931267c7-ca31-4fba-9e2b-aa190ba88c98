package com.qunar.strategy.visual.service;

import com.qunar.strategy.framework.core.annotation.UnitExecutorComponent;
import com.qunar.strategy.framework.core.executor.UnitExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * 执行器发现服务
 * 用于发现系统中所有可用的执行器及其配置信息
 */
@Service
public class ExecutorDiscoveryService {
    
    private static final Logger log = LoggerFactory.getLogger(ExecutorDiscoveryService.class);
    
    @Autowired
    private ApplicationContext applicationContext;
    
    /**
     * 执行器元数据
     */
    public static class ExecutorMetadata {
        private String name;
        private String className;
        private String description;
        private List<ConfigParameter> parameters;
        private String category;
        
        public ExecutorMetadata() {}
        
        public ExecutorMetadata(String name, String className) {
            this.name = name;
            this.className = className;
            this.parameters = new ArrayList<>();
        }
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public List<ConfigParameter> getParameters() { return parameters; }
        public void setParameters(List<ConfigParameter> parameters) { this.parameters = parameters; }
        
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
    }
    
    /**
     * 配置参数
     */
    public static class ConfigParameter {
        private String name;
        private String type;
        private String description;
        private Object defaultValue;
        private boolean required;
        
        public ConfigParameter() {}
        
        public ConfigParameter(String name, String type, String description) {
            this.name = name;
            this.type = type;
            this.description = description;
        }
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public Object getDefaultValue() { return defaultValue; }
        public void setDefaultValue(Object defaultValue) { this.defaultValue = defaultValue; }
        
        public boolean isRequired() { return required; }
        public void setRequired(boolean required) { this.required = required; }
    }
    
    /**
     * 获取所有可用的执行器
     */
    public List<ExecutorMetadata> getAllExecutors() {
        List<ExecutorMetadata> executors = new ArrayList<>();
        
        // 获取所有UnitExecutor类型的Bean
        Map<String, UnitExecutor> executorBeans = applicationContext.getBeansOfType(UnitExecutor.class);
        
        for (Map.Entry<String, UnitExecutor> entry : executorBeans.entrySet()) {
            String beanName = entry.getKey();
            UnitExecutor executor = entry.getValue();
            
            ExecutorMetadata metadata = createExecutorMetadata(beanName, executor);
            executors.add(metadata);
        }
        
        return executors;
    }
    
    /**
     * 根据名称获取执行器元数据
     */
    public ExecutorMetadata getExecutorMetadata(String executorName) {
        if (!applicationContext.containsBean(executorName)) {
            return null;
        }
        
        Object bean = applicationContext.getBean(executorName);
        if (!(bean instanceof UnitExecutor)) {
            return null;
        }
        
        return createExecutorMetadata(executorName, (UnitExecutor) bean);
    }
    
    /**
     * 创建执行器元数据
     */
    private ExecutorMetadata createExecutorMetadata(String beanName, UnitExecutor executor) {
        Class<?> executorClass = executor.getClass();
        ExecutorMetadata metadata = new ExecutorMetadata(beanName, executorClass.getName());
        
        // 获取注解信息
        UnitExecutorComponent annotation = executorClass.getAnnotation(UnitExecutorComponent.class);
        if (annotation != null) {
            // 可以从注解中获取更多信息
        }
        
        // 分析类名确定分类
        metadata.setCategory(determineCategory(executorClass));
        
        // 分析配置参数（通过反射分析常见的配置字段）
        metadata.setParameters(analyzeConfigParameters(executorClass));
        
        return metadata;
    }
    
    /**
     * 确定执行器分类
     */
    private String determineCategory(Class<?> executorClass) {
        String className = executorClass.getSimpleName().toLowerCase();
        String packageName = executorClass.getPackage().getName();
        
        if (packageName.contains("recall") || className.contains("recall")) {
            return "召回";
        } else if (packageName.contains("rerank") || className.contains("rerank") || className.contains("sort")) {
            return "重排";
        } else if (packageName.contains("filter") || className.contains("filter")) {
            return "过滤";
        } else if (packageName.contains("context") || className.contains("context")) {
            return "上下文";
        } else {
            return "其他";
        }
    }
    
    /**
     * 分析配置参数
     */
    private List<ConfigParameter> analyzeConfigParameters(Class<?> executorClass) {
        List<ConfigParameter> parameters = new ArrayList<>();
        
        // 这里可以通过反射分析类中的字段，或者通过注解来定义配置参数
        // 为了简化，这里提供一些常见的配置参数
        
        String category = determineCategory(executorClass);
        switch (category) {
            case "重排":
                parameters.add(new ConfigParameter("startPosition", "Integer", "开始位置"));
                parameters.add(new ConfigParameter("endPosition", "Integer", "结束位置"));
                parameters.add(new ConfigParameter("itemsKey", "String", "数据键名"));
                break;
            case "过滤":
                parameters.add(new ConfigParameter("filterCondition", "String", "过滤条件"));
                parameters.add(new ConfigParameter("itemsKey", "String", "数据键名"));
                break;
            case "召回":
                parameters.add(new ConfigParameter("recallSize", "Integer", "召回数量"));
                parameters.add(new ConfigParameter("dataSource", "String", "数据源"));
                break;
        }
        
        return parameters;
    }
}
