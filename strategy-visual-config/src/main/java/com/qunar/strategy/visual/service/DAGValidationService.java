package com.qunar.strategy.visual.service;

import com.qunar.strategy.visual.model.*;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * DAG验证服务
 * 验证策略DAG的正确性
 */
@Service
public class DAGValidationService {
    
    /**
     * 验证结果
     */
    public static class ValidationResult {
        private boolean valid;
        private List<String> errors;
        private List<String> warnings;
        
        public ValidationResult() {
            this.errors = new ArrayList<>();
            this.warnings = new ArrayList<>();
        }
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
        
        public List<String> getWarnings() { return warnings; }
        public void setWarnings(List<String> warnings) { this.warnings = warnings; }
        
        public void addError(String error) { this.errors.add(error); }
        public void addWarning(String warning) { this.warnings.add(warning); }
    }
    
    /**
     * 验证策略DAG
     */
    public ValidationResult validateDAG(StrategyDAG dag) {
        ValidationResult result = new ValidationResult();
        
        if (dag == null) {
            result.addError("策略DAG不能为空");
            result.setValid(false);
            return result;
        }
        
        // 基本信息验证
        validateBasicInfo(dag, result);
        
        // 节点验证
        validateNodes(dag, result);
        
        // 边验证
        validateEdges(dag, result);
        
        // 拓扑结构验证
        validateTopology(dag, result);
        
        // 业务逻辑验证
        validateBusinessLogic(dag, result);
        
        result.setValid(result.getErrors().isEmpty());
        return result;
    }
    
    /**
     * 验证基本信息
     */
    private void validateBasicInfo(StrategyDAG dag, ValidationResult result) {
        if (dag.getStrategyId() == null || dag.getStrategyId().trim().isEmpty()) {
            result.addError("策略ID不能为空");
        }
        
        if (dag.getStrategyName() == null || dag.getStrategyName().trim().isEmpty()) {
            result.addError("策略名称不能为空");
        }
    }
    
    /**
     * 验证节点
     */
    private void validateNodes(StrategyDAG dag, ValidationResult result) {
        List<VisualNode> nodes = dag.getNodes();
        if (nodes == null || nodes.isEmpty()) {
            result.addError("策略必须包含至少一个节点");
            return;
        }
        
        // 检查节点ID唯一性
        Set<String> nodeIds = new HashSet<>();
        for (VisualNode node : nodes) {
            if (node.getId() == null || node.getId().trim().isEmpty()) {
                result.addError("节点ID不能为空");
                continue;
            }
            
            if (nodeIds.contains(node.getId())) {
                result.addError("节点ID重复: " + node.getId());
            }
            nodeIds.add(node.getId());
            
            // 验证具体节点类型
            validateSpecificNode(node, result);
        }
        
        // 检查是否有开始和结束节点
        boolean hasStart = nodes.stream().anyMatch(n -> "start".equals(n.getNodeType()));
        boolean hasEnd = nodes.stream().anyMatch(n -> "end".equals(n.getNodeType()));
        
        if (!hasStart) {
            result.addWarning("建议添加开始节点");
        }
        
        if (!hasEnd) {
            result.addWarning("建议添加结束节点");
        }
    }
    
    /**
     * 验证具体节点
     */
    private void validateSpecificNode(VisualNode node, ValidationResult result) {
        if (node instanceof ExecutionUnitNode) {
            ExecutionUnitNode unitNode = (ExecutionUnitNode) node;
            if (unitNode.getExecutorClass() == null || unitNode.getExecutorClass().trim().isEmpty()) {
                result.addError("执行单元节点必须指定执行器类: " + node.getId());
            }
        } else if (node instanceof StageNode) {
            StageNode stageNode = (StageNode) node;
            if (stageNode.getStageId() == null || stageNode.getStageId().trim().isEmpty()) {
                result.addError("阶段节点必须指定阶段ID: " + node.getId());
            }
        }
    }
    
    /**
     * 验证边
     */
    private void validateEdges(StrategyDAG dag, ValidationResult result) {
        List<VisualEdge> edges = dag.getEdges();
        List<VisualNode> nodes = dag.getNodes();
        
        if (edges == null) {
            return;
        }
        
        Set<String> nodeIds = nodes.stream().map(VisualNode::getId).collect(Collectors.toSet());
        
        for (VisualEdge edge : edges) {
            if (edge.getSourceId() == null || edge.getTargetId() == null) {
                result.addError("边的源节点和目标节点不能为空");
                continue;
            }
            
            if (!nodeIds.contains(edge.getSourceId())) {
                result.addError("边的源节点不存在: " + edge.getSourceId());
            }
            
            if (!nodeIds.contains(edge.getTargetId())) {
                result.addError("边的目标节点不存在: " + edge.getTargetId());
            }
        }
    }
    
    /**
     * 验证拓扑结构
     */
    private void validateTopology(StrategyDAG dag, ValidationResult result) {
        List<VisualNode> nodes = dag.getNodes();
        List<VisualEdge> edges = dag.getEdges();
        
        if (nodes == null || edges == null) {
            return;
        }
        
        // 构建邻接表
        Map<String, List<String>> adjacencyList = new HashMap<>();
        for (VisualNode node : nodes) {
            adjacencyList.put(node.getId(), new ArrayList<>());
        }
        
        for (VisualEdge edge : edges) {
            if (edge.getSourceId() != null && edge.getTargetId() != null) {
                adjacencyList.get(edge.getSourceId()).add(edge.getTargetId());
            }
        }
        
        // 检查是否有环
        if (hasCycle(adjacencyList)) {
            result.addError("策略DAG中存在环，这是不允许的");
        }
        
        // 检查连通性
        validateConnectivity(adjacencyList, result);
    }
    
    /**
     * 检查是否有环
     */
    private boolean hasCycle(Map<String, List<String>> adjacencyList) {
        Set<String> visited = new HashSet<>();
        Set<String> recursionStack = new HashSet<>();
        
        for (String node : adjacencyList.keySet()) {
            if (hasCycleDFS(node, adjacencyList, visited, recursionStack)) {
                return true;
            }
        }
        
        return false;
    }
    
    private boolean hasCycleDFS(String node, Map<String, List<String>> adjacencyList, 
                               Set<String> visited, Set<String> recursionStack) {
        if (recursionStack.contains(node)) {
            return true;
        }
        
        if (visited.contains(node)) {
            return false;
        }
        
        visited.add(node);
        recursionStack.add(node);
        
        for (String neighbor : adjacencyList.get(node)) {
            if (hasCycleDFS(neighbor, adjacencyList, visited, recursionStack)) {
                return true;
            }
        }
        
        recursionStack.remove(node);
        return false;
    }
    
    /**
     * 验证连通性
     */
    private void validateConnectivity(Map<String, List<String>> adjacencyList, ValidationResult result) {
        // 检查是否有孤立节点
        for (Map.Entry<String, List<String>> entry : adjacencyList.entrySet()) {
            String nodeId = entry.getKey();
            List<String> neighbors = entry.getValue();
            
            // 检查是否有入边
            boolean hasIncoming = adjacencyList.values().stream()
                    .anyMatch(list -> list.contains(nodeId));
            
            // 检查是否有出边
            boolean hasOutgoing = !neighbors.isEmpty();
            
            if (!hasIncoming && !hasOutgoing) {
                result.addWarning("节点 " + nodeId + " 是孤立节点，没有连接到其他节点");
            }
        }
    }
    
    /**
     * 验证业务逻辑
     */
    private void validateBusinessLogic(StrategyDAG dag, ValidationResult result) {
        List<VisualNode> nodes = dag.getNodes();
        if (nodes == null) {
            return;
        }
        
        // 检查阶段和执行单元的层次关系
        List<StageNode> stageNodes = nodes.stream()
                .filter(n -> n instanceof StageNode)
                .map(n -> (StageNode) n)
                .collect(Collectors.toList());
        
        List<ExecutionUnitNode> unitNodes = nodes.stream()
                .filter(n -> n instanceof ExecutionUnitNode)
                .map(n -> (ExecutionUnitNode) n)
                .collect(Collectors.toList());
        
        if (!stageNodes.isEmpty() && !unitNodes.isEmpty()) {
            // 验证执行单元是否都属于某个阶段
            // 这里可以根据边的连接关系来判断
            result.addWarning("请确保执行单元节点正确连接到阶段节点");
        }
        
        // 检查互斥组
        validateMutexGroups(unitNodes, result);
    }
    
    /**
     * 验证互斥组
     */
    private void validateMutexGroups(List<ExecutionUnitNode> unitNodes, ValidationResult result) {
        Map<String, List<ExecutionUnitNode>> mutexGroups = new HashMap<>();
        
        for (ExecutionUnitNode unit : unitNodes) {
            String mutexGroup = unit.getMutexGroup();
            if (mutexGroup != null && !mutexGroup.trim().isEmpty()) {
                mutexGroups.computeIfAbsent(mutexGroup, k -> new ArrayList<>()).add(unit);
            }
        }
        
        for (Map.Entry<String, List<ExecutionUnitNode>> entry : mutexGroups.entrySet()) {
            String groupName = entry.getKey();
            List<ExecutionUnitNode> groupUnits = entry.getValue();
            
            if (groupUnits.size() < 2) {
                result.addWarning("互斥组 " + groupName + " 只有一个执行单元，建议移除互斥组设置");
            }
            
            // 检查优先级设置
            boolean hasPriority = groupUnits.stream().anyMatch(u -> u.getPriority() != Integer.MAX_VALUE);
            if (!hasPriority) {
                result.addWarning("互斥组 " + groupName + " 中的执行单元没有设置优先级");
            }
        }
    }
}
