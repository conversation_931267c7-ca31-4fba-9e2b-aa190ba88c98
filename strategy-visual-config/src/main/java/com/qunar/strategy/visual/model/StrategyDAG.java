package com.qunar.strategy.visual.model;

import java.util.List;
import java.util.Map;

/**
 * 策略DAG模型
 */
public class StrategyDAG {
    
    /**
     * 策略ID
     */
    private String strategyId;
    
    /**
     * 策略名称
     */
    private String strategyName;
    
    /**
     * 策略描述
     */
    private String description;
    
    /**
     * 节点列表
     */
    private List<VisualNode> nodes;
    
    /**
     * 边列表
     */
    private List<VisualEdge> edges;
    
    /**
     * 全局配置
     */
    private Map<String, Object> globalConfig;
    
    /**
     * 版本信息
     */
    private String version;
    
    /**
     * 创建时间
     */
    private Long createTime;
    
    /**
     * 更新时间
     */
    private Long updateTime;
    
    public StrategyDAG() {}
    
    public StrategyDAG(String strategyId, String strategyName) {
        this.strategyId = strategyId;
        this.strategyName = strategyName;
        this.createTime = System.currentTimeMillis();
        this.updateTime = System.currentTimeMillis();
    }
    
    public String getStrategyId() {
        return strategyId;
    }
    
    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId;
    }
    
    public String getStrategyName() {
        return strategyName;
    }
    
    public void setStrategyName(String strategyName) {
        this.strategyName = strategyName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public List<VisualNode> getNodes() {
        return nodes;
    }
    
    public void setNodes(List<VisualNode> nodes) {
        this.nodes = nodes;
    }
    
    public List<VisualEdge> getEdges() {
        return edges;
    }
    
    public void setEdges(List<VisualEdge> edges) {
        this.edges = edges;
    }
    
    public Map<String, Object> getGlobalConfig() {
        return globalConfig;
    }
    
    public void setGlobalConfig(Map<String, Object> globalConfig) {
        this.globalConfig = globalConfig;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public Long getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }
    
    public Long getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
