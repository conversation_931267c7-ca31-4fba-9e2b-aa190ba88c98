package com.qunar.strategy.visual.model;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import java.util.Map;

// 添加其他节点类型
class StartNode extends VisualNode {
    public StartNode() {
        super();
        setNodeType("start");
    }

    public StartNode(String id, String name) {
        super(id, name, "start");
    }
}

class EndNode extends VisualNode {
    public EndNode() {
        super();
        setNodeType("end");
    }

    public EndNode(String id, String name) {
        super(id, name, "end");
    }
}

class ConditionNode extends VisualNode {
    private String condition;

    public ConditionNode() {
        super();
        setNodeType("condition");
    }

    public ConditionNode(String id, String name) {
        super(id, name, "condition");
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }
}

class MutexGroupNode extends VisualNode {
    private String groupName;

    public MutexGroupNode() {
        super();
        setNodeType("mutexGroup");
    }

    public MutexGroupNode(String id, String name) {
        super(id, name, "mutexGroup");
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
}

/**
 * 可视化节点基类
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "nodeType")
@JsonSubTypes({
    @JsonSubTypes.Type(value = StartNode.class, name = "start"),
    @JsonSubTypes.Type(value = StageNode.class, name = "stage"),
    @JsonSubTypes.Type(value = ExecutionUnitNode.class, name = "executionUnit"),
    @JsonSubTypes.Type(value = ConditionNode.class, name = "condition"),
    @JsonSubTypes.Type(value = MutexGroupNode.class, name = "mutexGroup"),
    @JsonSubTypes.Type(value = EndNode.class, name = "end")
})
public abstract class VisualNode {
    
    /**
     * 节点ID
     */
    private String id;
    
    /**
     * 节点名称
     */
    private String name;
    
    /**
     * 节点描述
     */
    private String description;
    
    /**
     * 节点类型
     */
    private String nodeType;
    
    /**
     * 位置信息
     */
    private Position position;
    
    /**
     * 节点配置参数
     */
    private Map<String, Object> config;
    
    public VisualNode() {}
    
    public VisualNode(String id, String name, String nodeType) {
        this.id = id;
        this.name = name;
        this.nodeType = nodeType;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getNodeType() {
        return nodeType;
    }
    
    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }
    
    public Position getPosition() {
        return position;
    }
    
    public void setPosition(Position position) {
        this.position = position;
    }
    
    public Map<String, Object> getConfig() {
        return config;
    }
    
    public void setConfig(Map<String, Object> config) {
        this.config = config;
    }
    
    /**
     * 位置信息
     */
    public static class Position {
        private double x;
        private double y;
        
        public Position() {}
        
        public Position(double x, double y) {
            this.x = x;
            this.y = y;
        }
        
        public double getX() {
            return x;
        }
        
        public void setX(double x) {
            this.x = x;
        }
        
        public double getY() {
            return y;
        }
        
        public void setY(double y) {
            this.y = y;
        }
    }
}
