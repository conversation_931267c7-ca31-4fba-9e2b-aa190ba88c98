package com.qunar.strategy.visual.model;

/**
 * 执行单元节点
 */
public class ExecutionUnitNode extends VisualNode {
    
    /**
     * 执行器类名
     */
    private String executorClass;
    
    /**
     * 条件表达式
     */
    private String condition;
    
    /**
     * 互斥组
     */
    private String mutexGroup;
    
    /**
     * 优先级
     */
    private int priority = Integer.MAX_VALUE;
    
    /**
     * 执行顺序
     */
    private int order;
    
    public ExecutionUnitNode() {
        super();
        setNodeType("executionUnit");
    }
    
    public ExecutionUnitNode(String id, String name) {
        super(id, name, "executionUnit");
    }
    
    public String getExecutorClass() {
        return executorClass;
    }
    
    public void setExecutorClass(String executorClass) {
        this.executorClass = executorClass;
    }
    
    public String getCondition() {
        return condition;
    }
    
    public void setCondition(String condition) {
        this.condition = condition;
    }
    
    public String getMutexGroup() {
        return mutexGroup;
    }
    
    public void setMutexGroup(String mutexGroup) {
        this.mutexGroup = mutexGroup;
    }
    
    public int getPriority() {
        return priority;
    }
    
    public void setPriority(int priority) {
        this.priority = priority;
    }
    
    public int getOrder() {
        return order;
    }
    
    public void setOrder(int order) {
        this.order = order;
    }
}
