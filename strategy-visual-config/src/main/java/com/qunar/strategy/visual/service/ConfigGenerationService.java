package com.qunar.strategy.visual.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qunar.strategy.framework.common.model.*;
import com.qunar.strategy.visual.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 配置生成服务
 * 将可视化DAG转换为策略引擎配置
 */
@Service
public class ConfigGenerationService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 生成策略配置
     */
    public Strategy generateStrategyConfig(StrategyDAG dag) {
        Strategy strategy = new Strategy();
        strategy.setStrategyId(dag.getStrategyId());
        strategy.setStrategyName(dag.getStrategyName());
        strategy.setDescription(dag.getDescription());
        strategy.setConfiguration(dag.getGlobalConfig());
        
        // 生成阶段列表
        List<Stage> stages = generateStages(dag);
        strategy.setStages(stages);
        
        return strategy;
    }
    
    /**
     * 生成阶段列表
     */
    private List<Stage> generateStages(StrategyDAG dag) {
        List<VisualNode> nodes = dag.getNodes();
        List<VisualEdge> edges = dag.getEdges();
        
        // 获取所有阶段节点
        List<StageNode> stageNodes = nodes.stream()
                .filter(n -> n instanceof StageNode)
                .map(n -> (StageNode) n)
                .sorted(Comparator.comparingInt(StageNode::getOrder))
                .collect(Collectors.toList());
        
        List<Stage> stages = new ArrayList<>();
        
        for (StageNode stageNode : stageNodes) {
            Stage stage = new Stage();
            stage.setStageId(stageNode.getStageId());
            stage.setStageName(stageNode.getName());
            stage.setDescription(stageNode.getDescription());
            stage.setCondition(stageNode.getCondition());
            
            // 获取该阶段下的执行单元
            List<ExecutionUnit> executionUnits = getExecutionUnitsForStage(stageNode, dag);
            stage.setExecutionUnits(executionUnits);
            
            stages.add(stage);
        }
        
        // 如果没有阶段节点，创建默认阶段
        if (stages.isEmpty()) {
            Stage defaultStage = createDefaultStage(dag);
            if (defaultStage != null) {
                stages.add(defaultStage);
            }
        }
        
        return stages;
    }
    
    /**
     * 获取阶段下的执行单元
     */
    private List<ExecutionUnit> getExecutionUnitsForStage(StageNode stageNode, StrategyDAG dag) {
        List<VisualNode> nodes = dag.getNodes();
        List<VisualEdge> edges = dag.getEdges();
        
        // 找到连接到该阶段的执行单元节点
        Set<String> connectedUnitIds = edges.stream()
                .filter(e -> stageNode.getId().equals(e.getSourceId()) || stageNode.getId().equals(e.getTargetId()))
                .map(e -> stageNode.getId().equals(e.getSourceId()) ? e.getTargetId() : e.getSourceId())
                .collect(Collectors.toSet());
        
        List<ExecutionUnitNode> unitNodes = nodes.stream()
                .filter(n -> n instanceof ExecutionUnitNode && connectedUnitIds.contains(n.getId()))
                .map(n -> (ExecutionUnitNode) n)
                .sorted(Comparator.comparingInt(ExecutionUnitNode::getOrder))
                .collect(Collectors.toList());
        
        return unitNodes.stream()
                .map(this::convertToExecutionUnit)
                .collect(Collectors.toList());
    }
    
    /**
     * 创建默认阶段
     */
    private Stage createDefaultStage(StrategyDAG dag) {
        List<VisualNode> nodes = dag.getNodes();
        
        // 获取所有执行单元节点
        List<ExecutionUnitNode> unitNodes = nodes.stream()
                .filter(n -> n instanceof ExecutionUnitNode)
                .map(n -> (ExecutionUnitNode) n)
                .sorted(Comparator.comparingInt(ExecutionUnitNode::getOrder))
                .collect(Collectors.toList());
        
        if (unitNodes.isEmpty()) {
            return null;
        }
        
        Stage stage = new Stage();
        stage.setStageId("default_stage");
        stage.setStageName("默认阶段");
        stage.setDescription("自动生成的默认阶段");
        stage.setCondition("true");
        
        List<ExecutionUnit> executionUnits = unitNodes.stream()
                .map(this::convertToExecutionUnit)
                .collect(Collectors.toList());
        stage.setExecutionUnits(executionUnits);
        
        return stage;
    }
    
    /**
     * 转换为执行单元
     */
    private ExecutionUnit convertToExecutionUnit(ExecutionUnitNode unitNode) {
        ExecutionUnit unit = new ExecutionUnit();
        unit.setUnitId(unitNode.getId());
        unit.setUnitName(unitNode.getName());
        unit.setDescription(unitNode.getDescription());
        unit.setComponentName(unitNode.getExecutorClass());
        unit.setCondition(unitNode.getCondition());
        unit.setMutexGroup(unitNode.getMutexGroup());
        unit.setPriority(unitNode.getPriority());
        unit.setConfiguration(unitNode.getConfig());
        
        return unit;
    }
    
    /**
     * 生成路由配置
     */
    public Map<String, StrategyRouteRule> generateRouteConfig(List<RouteRuleConfig> routeConfigs) {
        Map<String, StrategyRouteRule> routes = new HashMap<>();
        
        for (RouteRuleConfig config : routeConfigs) {
            StrategyRouteRule rule = new StrategyRouteRule();
            rule.setConfigFileName(config.getConfigFileName());
            rule.setActiveVersion(config.getActiveVersion());
            rule.setCondition(config.getCondition());
            rule.setPriority(config.getPriority());
            
            routes.put(config.getStrategyId(), rule);
        }
        
        return routes;
    }
    
    /**
     * 路由规则配置
     */
    public static class RouteRuleConfig {
        private String strategyId;
        private String configFileName;
        private String activeVersion;
        private String condition;
        private int priority;
        
        public RouteRuleConfig() {}
        
        public RouteRuleConfig(String strategyId, String configFileName, String condition, int priority) {
            this.strategyId = strategyId;
            this.configFileName = configFileName;
            this.condition = condition;
            this.priority = priority;
            this.activeVersion = generateVersion();
        }
        
        private String generateVersion() {
            return String.valueOf(System.currentTimeMillis());
        }
        
        // Getters and Setters
        public String getStrategyId() { return strategyId; }
        public void setStrategyId(String strategyId) { this.strategyId = strategyId; }
        
        public String getConfigFileName() { return configFileName; }
        public void setConfigFileName(String configFileName) { this.configFileName = configFileName; }
        
        public String getActiveVersion() { return activeVersion; }
        public void setActiveVersion(String activeVersion) { this.activeVersion = activeVersion; }
        
        public String getCondition() { return condition; }
        public void setCondition(String condition) { this.condition = condition; }
        
        public int getPriority() { return priority; }
        public void setPriority(int priority) { this.priority = priority; }
    }
    
    /**
     * 生成JSON配置字符串
     */
    public String generateStrategyJson(StrategyDAG dag) throws Exception {
        Strategy strategy = generateStrategyConfig(dag);
        return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(strategy);
    }
    
    /**
     * 生成路由JSON配置字符串
     */
    public String generateRouteJson(List<RouteRuleConfig> routeConfigs) throws Exception {
        Map<String, StrategyRouteRule> routes = generateRouteConfig(routeConfigs);
        return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(routes);
    }
    
    /**
     * 验证并生成配置
     */
    public GenerationResult generateConfigs(StrategyDAG dag, List<RouteRuleConfig> routeConfigs) {
        GenerationResult result = new GenerationResult();
        
        try {
            // 生成策略配置
            String strategyJson = generateStrategyJson(dag);
            result.setStrategyConfig(strategyJson);
            
            // 生成路由配置
            if (routeConfigs != null && !routeConfigs.isEmpty()) {
                String routeJson = generateRouteJson(routeConfigs);
                result.setRouteConfig(routeJson);
            }
            
            result.setSuccess(true);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 生成结果
     */
    public static class GenerationResult {
        private boolean success;
        private String strategyConfig;
        private String routeConfig;
        private String errorMessage;
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getStrategyConfig() { return strategyConfig; }
        public void setStrategyConfig(String strategyConfig) { this.strategyConfig = strategyConfig; }
        
        public String getRouteConfig() { return routeConfig; }
        public void setRouteConfig(String routeConfig) { this.routeConfig = routeConfig; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
