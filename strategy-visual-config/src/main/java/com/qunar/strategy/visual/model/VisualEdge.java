package com.qunar.strategy.visual.model;

/**
 * 可视化连接边
 */
public class VisualEdge {
    
    /**
     * 边ID
     */
    private String id;
    
    /**
     * 源节点ID
     */
    private String sourceId;
    
    /**
     * 目标节点ID
     */
    private String targetId;
    
    /**
     * 边类型
     */
    private String edgeType;
    
    /**
     * 边标签
     */
    private String label;
    
    /**
     * 条件表达式（用于条件边）
     */
    private String condition;
    
    public VisualEdge() {}
    
    public VisualEdge(String id, String sourceId, String targetId) {
        this.id = id;
        this.sourceId = sourceId;
        this.targetId = targetId;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getSourceId() {
        return sourceId;
    }
    
    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }
    
    public String getTargetId() {
        return targetId;
    }
    
    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }
    
    public String getEdgeType() {
        return edgeType;
    }
    
    public void setEdgeType(String edgeType) {
        this.edgeType = edgeType;
    }
    
    public String getLabel() {
        return label;
    }
    
    public void setLabel(String label) {
        this.label = label;
    }
    
    public String getCondition() {
        return condition;
    }
    
    public void setCondition(String condition) {
        this.condition = condition;
    }
}
