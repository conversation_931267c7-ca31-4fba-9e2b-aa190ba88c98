package com.qunar.strategy.visual.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // 将根路径请求转发到 index.html
        registry.addViewController("/").setViewName("/index.html");
    }

    @Override
    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
        // 启用默认的 servlet 处理
        configurer.enable();
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 只处理静态资源路径，不要使用 /**
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/", "/webapp/static/");
        
        registry.addResourceHandler("/css/**")
                .addResourceLocations("classpath:/static/css/", "/webapp/css/");
        
        registry.addResourceHandler("/js/**")
                .addResourceLocations("classpath:/static/js/", "/webapp/js/");
        
        registry.addResourceHandler("/images/**")
                .addResourceLocations("classpath:/static/images/", "/webapp/images/");
        
        registry.addResourceHandler("/fonts/**")
                .addResourceLocations("classpath:/static/fonts/", "/webapp/fonts/");
        
        // 处理根目录下的静态文件（如index.html）
        registry.addResourceHandler("/index.html")
                .addResourceLocations("classpath:/static/", "/webapp/");
    }
}
