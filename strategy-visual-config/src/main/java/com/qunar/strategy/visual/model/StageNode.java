package com.qunar.strategy.visual.model;

/**
 * 阶段节点
 */
public class StageNode extends VisualNode {
    
    /**
     * 阶段ID
     */
    private String stageId;
    
    /**
     * 条件表达式
     */
    private String condition;
    
    /**
     * 执行顺序
     */
    private int order;
    
    public StageNode() {
        super();
        setNodeType("stage");
    }
    
    public StageNode(String id, String name) {
        super(id, name, "stage");
    }
    
    public String getStageId() {
        return stageId;
    }
    
    public void setStageId(String stageId) {
        this.stageId = stageId;
    }
    
    public String getCondition() {
        return condition;
    }
    
    public void setCondition(String condition) {
        this.condition = condition;
    }
    
    public int getOrder() {
        return order;
    }
    
    public void setOrder(int order) {
        this.order = order;
    }
}
