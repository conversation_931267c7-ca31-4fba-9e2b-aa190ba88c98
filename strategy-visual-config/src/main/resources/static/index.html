<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略引擎可视化配置系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="https://unpkg.com/@antv/g6/dist/g6.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }
        
        .header {
            background: #409EFF;
            color: white;
            padding: 16px 24px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 64px);
        }
        
        .sidebar {
            width: 300px;
            background: #f5f7fa;
            border-right: 1px solid #e4e7ed;
            overflow-y: auto;
        }
        
        .canvas-container {
            flex: 1;
            position: relative;
            background: #fff;
        }
        
        .properties-panel {
            width: 350px;
            background: #f5f7fa;
            border-left: 1px solid #e4e7ed;
            overflow-y: auto;
        }
        
        .toolbar {
            padding: 16px;
            border-bottom: 1px solid #e4e7ed;
            background: white;
        }
        
        .node-palette {
            padding: 16px;
        }
        
        .node-category {
            margin-bottom: 16px;
        }
        
        .node-category h3 {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: #606266;
        }
        
        .node-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 4px;
            background: white;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .node-item:hover {
            border-color: #409EFF;
            background: #ecf5ff;
        }
        
        .node-item .icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            border-radius: 50%;
        }
        
        .node-item .start { background: #67c23a; }
        .node-item .stage { background: #409eff; }
        .node-item .execution { background: #e6a23c; }
        .node-item .condition { background: #f56c6c; }
        .node-item .mutex { background: #909399; }
        .node-item .end { background: #67c23a; }
        
        .canvas {
            width: 100%;
            height: 100%;
        }
        
        .properties-content {
            padding: 16px;
        }
        
        .property-group {
            margin-bottom: 24px;
        }
        
        .property-group h3 {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #303133;
            border-bottom: 1px solid #e4e7ed;
            padding-bottom: 8px;
        }
        
        .expression-editor {
            position: relative;
        }
        
        .expression-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e4e7ed;
            border-top: none;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .suggestion-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f5f7fa;
        }
        
        .suggestion-item:hover {
            background: #f5f7fa;
        }
        
        .validation-message {
            margin-top: 4px;
            font-size: 12px;
        }
        
        .validation-error {
            color: #f56c6c;
        }
        
        .validation-warning {
            color: #e6a23c;
        }
        
        .validation-success {
            color: #67c23a;
        }
        
        .bottom-panel {
            position: fixed;
            bottom: 0;
            left: 300px;
            right: 350px;
            height: 200px;
            background: white;
            border-top: 1px solid #e4e7ed;
            transform: translateY(100%);
            transition: transform 0.3s;
            z-index: 100;
        }
        
        .bottom-panel.show {
            transform: translateY(0);
        }
        
        .config-preview {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .config-preview .header {
            padding: 8px 16px;
            background: #f5f7fa;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .config-preview .content {
            flex: 1;
            overflow: auto;
            padding: 16px;
        }
        
        .config-preview pre {
            margin: 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 头部 -->
        <div class="header">
            <h1>策略引擎可视化配置系统</h1>
        </div>
        
        <!-- 主容器 -->
        <div class="main-container">
            <!-- 左侧边栏 -->
            <div class="sidebar">
                <!-- 工具栏 -->
                <div class="toolbar">
                    <el-button-group>
                        <el-button size="small" @click="newStrategy">新建</el-button>
                        <el-button size="small" @click="saveStrategy">保存</el-button>
                        <el-button size="small" @click="loadStrategy">加载</el-button>
                    </el-button-group>
                    <el-button-group style="margin-left: 8px;">
                        <el-button size="small" @click="validateDAG">验证</el-button>
                        <el-button size="small" @click="generateConfig">生成配置</el-button>
                    </el-button-group>
                </div>
                
                <!-- 节点面板 -->
                <div class="node-palette">
                    <div class="node-category">
                        <h3>基础节点</h3>
                        <div class="node-item" draggable="true" @dragstart="onDragStart($event, 'start')">
                            <div class="icon start"></div>
                            <span>开始节点</span>
                        </div>
                        <div class="node-item" draggable="true" @dragstart="onDragStart($event, 'end')">
                            <div class="icon end"></div>
                            <span>结束节点</span>
                        </div>
                    </div>
                    
                    <div class="node-category">
                        <h3>流程节点</h3>
                        <div class="node-item" draggable="true" @dragstart="onDragStart($event, 'stage')">
                            <div class="icon stage"></div>
                            <span>阶段节点</span>
                        </div>
                        <div class="node-item" draggable="true" @dragstart="onDragStart($event, 'condition')">
                            <div class="icon condition"></div>
                            <span>条件节点</span>
                        </div>
                    </div>
                    
                    <div class="node-category">
                        <h3>执行节点</h3>
                        <div class="node-item" draggable="true" @dragstart="onDragStart($event, 'executionUnit')">
                            <div class="icon execution"></div>
                            <span>执行单元</span>
                        </div>
                        <div class="node-item" draggable="true" @dragstart="onDragStart($event, 'mutexGroup')">
                            <div class="icon mutex"></div>
                            <span>互斥组</span>
                        </div>
                    </div>
                    
                    <div class="node-category">
                        <h3>执行器列表</h3>
                        <div v-for="executor in executors" :key="executor.name" 
                             class="node-item" 
                             draggable="true" 
                             @dragstart="onDragStart($event, 'executionUnit', executor)">
                            <div class="icon execution"></div>
                            <div>
                                <div>{{ executor.name }}</div>
                                <div style="font-size: 12px; color: #909399;">{{ executor.category }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 画布区域 -->
            <div class="canvas-container" 
                 @drop="onDrop" 
                 @dragover="onDragOver">
                <div id="canvas" class="canvas"></div>
            </div>
            
            <!-- 右侧属性面板 -->
            <div class="properties-panel">
                <div class="properties-content">
                    <div v-if="selectedNode">
                        <div class="property-group">
                            <h3>基本信息</h3>
                            <el-form label-width="80px" size="small">
                                <el-form-item label="节点ID">
                                    <el-input v-model="selectedNode.id" :disabled="true"></el-input>
                                </el-form-item>
                                <el-form-item label="节点名称">
                                    <el-input v-model="selectedNode.name" @input="updateNode"></el-input>
                                </el-form-item>
                                <el-form-item label="描述">
                                    <el-input type="textarea" v-model="selectedNode.description" @input="updateNode"></el-input>
                                </el-form-item>
                            </el-form>
                        </div>
                        
                        <!-- 执行单元特有属性 -->
                        <div v-if="selectedNode.nodeType === 'executionUnit'" class="property-group">
                            <h3>执行器配置</h3>
                            <el-form label-width="80px" size="small">
                                <el-form-item label="执行器">
                                    <el-select v-model="selectedNode.executorClass" @change="updateNode" style="width: 100%">
                                        <el-option v-for="executor in executors" 
                                                   :key="executor.name" 
                                                   :label="executor.name" 
                                                   :value="executor.name">
                                            <span>{{ executor.name }}</span>
                                            <span style="float: right; color: #8492a6; font-size: 13px">{{ executor.category }}</span>
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="互斥组">
                                    <el-input v-model="selectedNode.mutexGroup" @input="updateNode"></el-input>
                                </el-form-item>
                                <el-form-item label="优先级">
                                    <el-input-number v-model="selectedNode.priority" @change="updateNode" style="width: 100%"></el-input-number>
                                </el-form-item>
                                <el-form-item label="执行顺序">
                                    <el-input-number v-model="selectedNode.order" @change="updateNode" style="width: 100%"></el-input-number>
                                </el-form-item>
                            </el-form>
                        </div>
                        
                        <!-- 条件表达式 -->
                        <div v-if="selectedNode.nodeType === 'executionUnit' || selectedNode.nodeType === 'stage' || selectedNode.nodeType === 'condition'" class="property-group">
                            <h3>条件表达式</h3>
                            <div class="expression-editor">
                                <el-input type="textarea" 
                                          v-model="selectedNode.condition" 
                                          @input="onExpressionInput"
                                          placeholder="请输入条件表达式，如：source == 'mini' && hasIdentity('newUser')">
                                </el-input>
                                <div v-if="showSuggestions" class="expression-suggestions">
                                    <div v-for="suggestion in expressionSuggestions" 
                                         :key="suggestion" 
                                         class="suggestion-item"
                                         @click="applySuggestion(suggestion)">
                                        {{ suggestion }}
                                    </div>
                                </div>
                                <div v-if="expressionValidation" class="validation-message">
                                    <div v-if="!expressionValidation.valid" class="validation-error">
                                        ❌ {{ expressionValidation.errorMessage }}
                                    </div>
                                    <div v-else class="validation-success">
                                        ✅ 表达式语法正确
                                    </div>
                                    <div v-for="warning in expressionValidation.warnings" 
                                         :key="warning" 
                                         class="validation-warning">
                                        ⚠️ {{ warning }}
                                    </div>
                                    <div v-for="suggestion in expressionValidation.suggestions" 
                                         :key="suggestion" 
                                         class="validation-warning">
                                        💡 {{ suggestion }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 配置参数 -->
                        <div v-if="selectedNode.config" class="property-group">
                            <h3>配置参数</h3>
                            <el-form label-width="80px" size="small">
                                <el-form-item v-for="(value, key) in selectedNode.config" 
                                              :key="key" 
                                              :label="key">
                                    <el-input v-model="selectedNode.config[key]" @input="updateNode"></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-button size="small" @click="addConfigParam">添加参数</el-button>
                                </el-form-item>
                            </el-form>
                        </div>
                    </div>
                    
                    <div v-else>
                        <el-empty description="请选择一个节点来编辑属性"></el-empty>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部配置预览面板 -->
        <div class="bottom-panel" :class="{ show: showConfigPreview }">
            <div class="config-preview">
                <div class="header">
                    <span>配置预览</span>
                    <el-button-group>
                        <el-button size="mini" @click="previewStrategy">策略配置</el-button>
                        <el-button size="mini" @click="previewRoutes">路由配置</el-button>
                        <el-button size="mini" @click="showConfigPreview = false">关闭</el-button>
                    </el-button-group>
                </div>
                <div class="content">
                    <pre>{{ configPreviewContent }}</pre>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 引入依赖 -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/@antv/g6/dist/g6.min.js"></script>
    <script src="js/strategy-visual-config.js"></script>
</body>
</html>
