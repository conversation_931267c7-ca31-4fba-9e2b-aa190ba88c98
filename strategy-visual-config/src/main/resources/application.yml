server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: strategy-visual-config
  
  # Jackson配置
  jackson:
    default-property-inclusion: non_null
    serialization:
      indent-output: true
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# 日志配置
logging:
  level:
    com.qunar.strategy: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 策略框架配置
strategy:
  framework:
    config-location-folder: classpath:strategy/strategies/
    route-config-location: classpath:strategy/routes.json
    enable-remote-config: false
    debug: true
