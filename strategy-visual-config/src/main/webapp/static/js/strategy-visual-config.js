// 策略可视化配置系统主要逻辑
new Vue({
    el: '#app',
    data() {
        return {
            // G6图实例
            graph: null,
            
            // 当前选中的节点
            selectedNode: null,
            
            // 可用的执行器列表
            executors: [],
            
            // 当前策略DAG
            currentDAG: {
                strategyId: '',
                strategyName: '',
                description: '',
                nodes: [],
                edges: [],
                globalConfig: {}
            },
            
            // 表达式相关
            expressionSuggestions: [],
            showSuggestions: false,
            expressionValidation: null,
            
            // 配置预览
            showConfigPreview: false,
            configPreviewContent: '',
            
            // 节点计数器
            nodeCounter: 0
        };
    },
    
    mounted() {
        this.initGraph();
        this.loadExecutors();
    },
    
    methods: {
        // 初始化G6图
        initGraph() {
            const container = document.getElementById('canvas');
            const width = container.scrollWidth;
            const height = container.scrollHeight;
            
            this.graph = new G6.Graph({
                container: 'canvas',
                width,
                height,
                modes: {
                    default: ['drag-canvas', 'zoom-canvas', 'drag-node', 'click-select']
                },
                defaultNode: {
                    type: 'rect',
                    size: [120, 60],
                    style: {
                        fill: '#f0f0f0',
                        stroke: '#666',
                        lineWidth: 1,
                        radius: 4
                    },
                    labelCfg: {
                        style: {
                            fill: '#333',
                            fontSize: 12
                        }
                    }
                },
                defaultEdge: {
                    type: 'polyline',
                    style: {
                        stroke: '#666',
                        lineWidth: 2,
                        endArrow: {
                            path: G6.Arrow.triangle(8, 8, 8),
                            fill: '#666'
                        }
                    },
                    labelCfg: {
                        autoRotate: true,
                        style: {
                            fill: '#333',
                            fontSize: 10
                        }
                    }
                },
                nodeStateStyles: {
                    selected: {
                        stroke: '#409EFF',
                        lineWidth: 3
                    }
                }
            });
            
            // 监听节点点击事件
            this.graph.on('node:click', (e) => {
                this.selectNode(e.item);
            });
            
            // 监听画布点击事件
            this.graph.on('canvas:click', () => {
                this.clearSelection();
            });
            
            // 监听节点拖拽结束事件
            this.graph.on('node:dragend', (e) => {
                this.updateNodePosition(e.item);
            });
        },
        
        // 加载执行器列表
        async loadExecutors() {
            try {
                const response = await fetch('/api/visual-config/executors');
                this.executors = await response.json();
            } catch (error) {
                console.error('加载执行器失败:', error);
                this.$message.error('加载执行器失败');
            }
        },
        
        // 拖拽开始
        onDragStart(event, nodeType, executorData = null) {
            event.dataTransfer.setData('nodeType', nodeType);
            if (executorData) {
                event.dataTransfer.setData('executorData', JSON.stringify(executorData));
            }
        },
        
        // 拖拽悬停
        onDragOver(event) {
            event.preventDefault();
        },
        
        // 拖拽放置
        onDrop(event) {
            event.preventDefault();
            const nodeType = event.dataTransfer.getData('nodeType');
            const executorData = event.dataTransfer.getData('executorData');
            
            const rect = event.currentTarget.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            this.createNode(nodeType, x, y, executorData ? JSON.parse(executorData) : null);
        },
        
        // 创建节点
        createNode(nodeType, x, y, executorData = null) {
            const nodeId = `${nodeType}_${++this.nodeCounter}`;
            const nodeName = this.getNodeTypeName(nodeType);
            
            const node = {
                id: nodeId,
                name: nodeName,
                nodeType: nodeType,
                description: '',
                position: { x, y },
                config: {}
            };
            
            // 设置节点特有属性
            if (nodeType === 'executionUnit') {
                node.executorClass = executorData ? executorData.name : '';
                node.condition = '';
                node.mutexGroup = '';
                node.priority = 0;
                node.order = 0;
            } else if (nodeType === 'stage') {
                node.stageId = nodeId;
                node.condition = '';
                node.order = 0;
            } else if (nodeType === 'condition') {
                node.condition = '';
            } else if (nodeType === 'mutexGroup') {
                node.groupName = '';
            }
            
            // 添加到DAG
            this.currentDAG.nodes.push(node);
            
            // 添加到图中
            this.addNodeToGraph(node);
        },
        
        // 添加节点到图中
        addNodeToGraph(node) {
            const graphNode = {
                id: node.id,
                label: node.name,
                x: node.position.x,
                y: node.position.y,
                style: this.getNodeStyle(node.nodeType),
                nodeType: node.nodeType
            };
            
            this.graph.addItem('node', graphNode);
        },
        
        // 获取节点样式
        getNodeStyle(nodeType) {
            const styles = {
                start: { fill: '#67c23a' },
                end: { fill: '#67c23a' },
                stage: { fill: '#409eff' },
                executionUnit: { fill: '#e6a23c' },
                condition: { fill: '#f56c6c' },
                mutexGroup: { fill: '#909399' }
            };
            return styles[nodeType] || { fill: '#f0f0f0' };
        },
        
        // 获取节点类型名称
        getNodeTypeName(nodeType) {
            const names = {
                start: '开始',
                end: '结束',
                stage: '阶段',
                executionUnit: '执行单元',
                condition: '条件',
                mutexGroup: '互斥组'
            };
            return names[nodeType] || nodeType;
        },
        
        // 选择节点
        selectNode(item) {
            // 清除之前的选择
            this.graph.getNodes().forEach(node => {
                this.graph.clearItemStates(node, 'selected');
            });
            
            // 选择当前节点
            this.graph.setItemState(item, 'selected', true);
            
            // 更新选中的节点数据
            const nodeId = item.getID();
            this.selectedNode = this.currentDAG.nodes.find(n => n.id === nodeId);
        },
        
        // 清除选择
        clearSelection() {
            this.graph.getNodes().forEach(node => {
                this.graph.clearItemStates(node, 'selected');
            });
            this.selectedNode = null;
        },
        
        // 更新节点
        updateNode() {
            if (!this.selectedNode) return;
            
            // 更新图中的节点标签
            const graphNode = this.graph.findById(this.selectedNode.id);
            if (graphNode) {
                this.graph.updateItem(graphNode, {
                    label: this.selectedNode.name
                });
            }
        },
        
        // 更新节点位置
        updateNodePosition(item) {
            const nodeId = item.getID();
            const node = this.currentDAG.nodes.find(n => n.id === nodeId);
            if (node) {
                const model = item.getModel();
                node.position.x = model.x;
                node.position.y = model.y;
            }
        },
        
        // 表达式输入处理
        async onExpressionInput() {
            if (!this.selectedNode || !this.selectedNode.condition) {
                this.showSuggestions = false;
                this.expressionValidation = null;
                return;
            }
            
            // 获取表达式建议
            try {
                const response = await fetch(`/api/visual-config/expression-suggestions?partial=${encodeURIComponent(this.selectedNode.condition)}`);
                this.expressionSuggestions = await response.json();
                this.showSuggestions = this.expressionSuggestions.length > 0;
            } catch (error) {
                console.error('获取表达式建议失败:', error);
            }
            
            // 验证表达式
            try {
                const response = await fetch('/api/visual-config/validate-expression', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        expression: this.selectedNode.condition
                    })
                });
                this.expressionValidation = await response.json();
            } catch (error) {
                console.error('验证表达式失败:', error);
            }
        },
        
        // 应用建议
        applySuggestion(suggestion) {
            if (this.selectedNode) {
                this.selectedNode.condition = suggestion;
                this.showSuggestions = false;
                this.updateNode();
            }
        },
        
        // 添加配置参数
        addConfigParam() {
            this.$prompt('请输入参数名称', '添加配置参数', {
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(({ value }) => {
                if (value && this.selectedNode) {
                    if (!this.selectedNode.config) {
                        this.selectedNode.config = {};
                    }
                    this.$set(this.selectedNode.config, value, '');
                }
            });
        },
        
        // 新建策略
        newStrategy() {
            this.$prompt('请输入策略名称', '新建策略', {
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(({ value }) => {
                if (value) {
                    this.currentDAG = {
                        strategyId: `strategy_${Date.now()}`,
                        strategyName: value,
                        description: '',
                        nodes: [],
                        edges: [],
                        globalConfig: {}
                    };
                    this.graph.clear();
                    this.selectedNode = null;
                    this.nodeCounter = 0;
                }
            });
        },
        
        // 保存策略
        async saveStrategy() {
            if (!this.currentDAG.strategyName) {
                this.$message.warning('请先创建策略');
                return;
            }
            
            try {
                const response = await fetch('/api/visual-config/save-dag', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.currentDAG)
                });
                
                const result = await response.json();
                if (result.success) {
                    this.$message.success('策略保存成功');
                } else {
                    this.$message.error('策略保存失败');
                }
            } catch (error) {
                console.error('保存策略失败:', error);
                this.$message.error('策略保存失败');
            }
        },
        
        // 加载策略
        loadStrategy() {
            // 这里可以实现策略选择和加载逻辑
            this.$message.info('加载策略功能待实现');
        },
        
        // 验证DAG
        async validateDAG() {
            try {
                const response = await fetch('/api/visual-config/validate-dag', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.currentDAG)
                });
                
                const result = await response.json();
                
                if (result.valid) {
                    this.$message.success('DAG验证通过');
                } else {
                    let message = '验证失败:\n' + result.errors.join('\n');
                    if (result.warnings.length > 0) {
                        message += '\n警告:\n' + result.warnings.join('\n');
                    }
                    this.$alert(message, '验证结果', {
                        type: 'warning'
                    });
                }
            } catch (error) {
                console.error('验证DAG失败:', error);
                this.$message.error('验证DAG失败');
            }
        },
        
        // 生成配置
        async generateConfig() {
            try {
                const response = await fetch('/api/visual-config/generate-config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        dag: this.currentDAG,
                        routeConfigs: []
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    this.configPreviewContent = result.strategyConfig;
                    this.showConfigPreview = true;
                    this.$message.success('配置生成成功');
                } else {
                    this.$message.error('配置生成失败: ' + result.errorMessage);
                }
            } catch (error) {
                console.error('生成配置失败:', error);
                this.$message.error('生成配置失败');
            }
        },
        
        // 预览策略配置
        async previewStrategy() {
            try {
                const response = await fetch('/api/visual-config/preview-strategy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.currentDAG)
                });
                
                this.configPreviewContent = await response.text();
                this.showConfigPreview = true;
            } catch (error) {
                console.error('预览策略配置失败:', error);
                this.$message.error('预览策略配置失败');
            }
        },
        
        // 预览路由配置
        async previewRoutes() {
            try {
                const routeConfigs = [{
                    strategyId: this.currentDAG.strategyId,
                    configFileName: `${this.currentDAG.strategyId}.json`,
                    condition: 'true',
                    priority: 0
                }];
                
                const response = await fetch('/api/visual-config/preview-routes', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(routeConfigs)
                });
                
                this.configPreviewContent = await response.text();
                this.showConfigPreview = true;
            } catch (error) {
                console.error('预览路由配置失败:', error);
                this.$message.error('预览路由配置失败');
            }
        }
    }
});
