<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>G6测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            margin-bottom: 20px;
        }
        
        .canvas {
            width: 800px;
            height: 600px;
            border: 1px solid #ccc;
            background: #f9f9f9;
        }
        
        .info {
            margin-top: 10px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
        }
        
        .error {
            color: red;
        }
        
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>G6图形库测试</h1>
    
    <div class="test-container">
        <h2>依赖检查</h2>
        <div id="dependencies-info" class="info"></div>
    </div>
    
    <div class="test-container">
        <h2>G6图形测试</h2>
        <div id="canvas" class="canvas"></div>
        <div id="graph-info" class="info"></div>
    </div>
    
    <div class="test-container">
        <button onclick="testSimpleGraph()">测试简单图形</button>
        <button onclick="testComplexGraph()">测试复杂图形</button>
        <button onclick="clearGraph()">清空图形</button>
    </div>
    
    <!-- 引入依赖 -->
    <script src="https://unpkg.com/@antv/g6@4.8.24/dist/g6.min.js"></script>
    
    <script>
        let graph = null;
        
        // 检查依赖
        function checkDependencies() {
            const info = document.getElementById('dependencies-info');
            const dependencies = {
                'G6': typeof G6 !== 'undefined'
            };
            
            let html = '';
            for (const [name, loaded] of Object.entries(dependencies)) {
                const status = loaded ? 'success' : 'error';
                const text = loaded ? '✓ 已加载' : '✗ 未加载';
                html += `<div class="${status}">${name}: ${text}</div>`;
            }
            
            if (typeof G6 !== 'undefined') {
                html += `<div class="success">G6版本: ${G6.version || 'unknown'}</div>`;
            }
            
            info.innerHTML = html;
        }
        
        // 初始化图形
        function initGraph() {
            const container = document.getElementById('canvas');
            const info = document.getElementById('graph-info');
            
            try {
                if (graph) {
                    graph.destroy();
                }
                
                graph = new G6.Graph({
                    container: 'canvas',
                    width: 800,
                    height: 600,
                    modes: {
                        default: ['drag-canvas', 'zoom-canvas', 'drag-node']
                    },
                    defaultNode: {
                        type: 'circle',
                        size: 60,
                        style: {
                            fill: '#f0f0f0',
                            stroke: '#666',
                            lineWidth: 2
                        },
                        labelCfg: {
                            style: {
                                fill: '#333',
                                fontSize: 12
                            }
                        }
                    },
                    defaultEdge: {
                        type: 'line',
                        style: {
                            stroke: '#666',
                            lineWidth: 2,
                            endArrow: true
                        }
                    }
                });
                
                info.innerHTML = '<div class="success">✓ G6图形初始化成功</div>';
                return true;
            } catch (error) {
                console.error('G6初始化失败:', error);
                info.innerHTML = `<div class="error">✗ G6图形初始化失败: ${error.message}</div>`;
                return false;
            }
        }
        
        // 测试简单图形
        function testSimpleGraph() {
            if (!initGraph()) return;
            
            const data = {
                nodes: [
                    { id: 'node1', label: '节点1', x: 200, y: 200 },
                    { id: 'node2', label: '节点2', x: 400, y: 200 }
                ],
                edges: [
                    { source: 'node1', target: 'node2' }
                ]
            };
            
            graph.data(data);
            graph.render();
            
            document.getElementById('graph-info').innerHTML = 
                '<div class="success">✓ 简单图形渲染成功</div>';
        }
        
        // 测试复杂图形
        function testComplexGraph() {
            if (!initGraph()) return;
            
            const data = {
                nodes: [
                    { id: 'start', label: '开始', x: 100, y: 300, style: { fill: '#67c23a' } },
                    { id: 'stage1', label: '阶段1', x: 300, y: 200, style: { fill: '#409eff' } },
                    { id: 'stage2', label: '阶段2', x: 300, y: 400, style: { fill: '#409eff' } },
                    { id: 'unit1', label: '执行单元1', x: 500, y: 200, style: { fill: '#e6a23c' } },
                    { id: 'unit2', label: '执行单元2', x: 500, y: 400, style: { fill: '#e6a23c' } },
                    { id: 'end', label: '结束', x: 700, y: 300, style: { fill: '#67c23a' } }
                ],
                edges: [
                    { source: 'start', target: 'stage1' },
                    { source: 'start', target: 'stage2' },
                    { source: 'stage1', target: 'unit1' },
                    { source: 'stage2', target: 'unit2' },
                    { source: 'unit1', target: 'end' },
                    { source: 'unit2', target: 'end' }
                ]
            };
            
            graph.data(data);
            graph.render();
            
            document.getElementById('graph-info').innerHTML = 
                '<div class="success">✓ 复杂图形渲染成功</div>';
        }
        
        // 清空图形
        function clearGraph() {
            if (graph) {
                graph.clear();
                document.getElementById('graph-info').innerHTML = 
                    '<div class="success">✓ 图形已清空</div>';
            }
        }
        
        // 页面加载完成后执行
        window.addEventListener('load', function() {
            console.log('页面加载完成');
            checkDependencies();
            
            // 延迟初始化，确保G6完全加载
            setTimeout(() => {
                testSimpleGraph();
            }, 1000);
        });
    </script>
</body>
</html>
