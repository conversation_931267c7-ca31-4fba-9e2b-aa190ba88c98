<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.qunar.strategy.framework</groupId>
        <artifactId>strategy-engine</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    
    <artifactId>strategy-visual-config</artifactId>
    <name>Strategy Visual Configuration</name>
    <description>可视化策略配置系统</description>
    
    <dependencies>
        <!-- Spring Boot Starter Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- Strategy Framework Core -->
        <dependency>
            <groupId>com.qunar.strategy.framework</groupId>
            <artifactId>strategy-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- Strategy Framework Common -->
        <dependency>
            <groupId>com.qunar.strategy.framework</groupId>
            <artifactId>strategy-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- Strategy Framework Context -->
        <dependency>
            <groupId>com.qunar.strategy.framework</groupId>
            <artifactId>strategy-context</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- Strategy Framework Recall -->
        <dependency>
            <groupId>com.qunar.strategy.framework</groupId>
            <artifactId>strategy-recall</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- Strategy Framework Rerank -->
        <dependency>
            <groupId>com.qunar.strategy.framework</groupId>
            <artifactId>strategy-rerank</artifactId>
            <version>${project.version}</version>
        </dependency>
        
        <!-- Jackson for JSON processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        
        <!-- Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        
        <!-- Test dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        
        <!-- JUnit 5 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
