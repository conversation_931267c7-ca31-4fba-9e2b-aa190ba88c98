<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>qunar.common</groupId>
        <artifactId>qunar-supom-generic</artifactId>
        <version>2.0.7</version>
    </parent>

    <groupId>com.qunar.strategy.visual</groupId>
    <artifactId>strategy-visual-config</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>war</packaging>
    <name>Strategy Visual Configuration</name>
    <description>可视化策略配置系统</description>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.version>2.5.5</spring-boot.version>
        <strategy.engine.version>1.0.1</strategy.engine.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.qunar.hotel</groupId>
            <artifactId>strategy-core</artifactId>
            <version>${strategy.engine.version}</version>
        </dependency>

        <dependency>
            <groupId>com.atlassian.elasticsearch.client</groupId>
            <artifactId>elasticsearch-client</artifactId>
            <version>5.3.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>jsr305</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.13.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>t-digest</artifactId>
                    <groupId>com.tdunning</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jopt-simple</artifactId>
                    <groupId>net.sf.jopt-simple</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>scala-reflect</artifactId>
                    <groupId>org.scala-lang</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.5</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>

        <!--<dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>-->
        <!--<dependency>
            <groupId>com.qunar.db</groupId>
            <artifactId>db-datasource</artifactId>
        </dependency>-->
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>qunar.tc.spring.cloud</groupId>
            <artifactId>spring-cloud-qunar-starter-qconfig</artifactId>
        </dependency>
        <dependency>
            <groupId>qunar.tc.spring.cloud</groupId>
            <artifactId>spring-cloud-qunar-starter-qmq</artifactId>
        </dependency>
        <dependency>
            <groupId>qunar.tc.spring.cloud</groupId>
            <artifactId>spring-cloud-qunar-starter-qschedule</artifactId>
        </dependency>
        <dependency>
            <groupId>qunar.tc.qclient</groupId>
            <artifactId>qclient-redis</artifactId>
        </dependency>-->
        <dependency>
            <groupId>qunar.tc.spring.cloud</groupId>
            <artifactId>spring-cloud-qunar-starter-dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>qunar.tc.spring.cloud</groupId>
            <artifactId>spring-cloud-qunar-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunar.flight</groupId>
            <artifactId>qmonitor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qunar.search</groupId>
            <artifactId>rank-common</artifactId>
            <version>0.0.580</version>
            <exclusions>
                <exclusion>
                    <artifactId>cbrs-over-hqmonitor</artifactId>
                    <groupId>com.qunar.tc</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hslist-common</artifactId>
                    <groupId>com.qunar.hotel</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lucene-core</artifactId>
                    <groupId>org.apache.lucene</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <source>${java_source_version}</source>
                        <target>${java_target_version}</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>3.2.3</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <finalName>h_rank_web</finalName>
    </build>
</project>
