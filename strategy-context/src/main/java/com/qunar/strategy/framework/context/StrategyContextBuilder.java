package com.qunar.strategy.framework.context;

import com.qunar.datateam.abtest.entities.ABTestsParam;
import com.qunar.strategy.framework.common.constants.ContextVarConst;
import com.qunar.strategy.framework.core.abtest.AbTestConfigRegistry;
import com.qunar.strategy.framework.common.model.ExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

public class StrategyContextBuilder {

    private static final Logger log = LoggerFactory.getLogger(StrategyContextBuilder.class);
    
    /**
     * 构建基础的执行上下文
     *
     * @param traceId 调用链ID
     * @param flowType 流量类型
     * @param userId 用户ID
     * @param abByValue ABTest分流的依据值
     * @return ExecutionContext
     */
    public ExecutionContext buildBasicContext(String traceId, String flowType,
                                              String userId, String abByValue, boolean debugMode) {
        ExecutionContext context = new ExecutionContext(userId);
        context.setDebugMode(debugMode);
        context.setUserId(userId);
        // 添加基础变量
        context.setVariable(ContextVarConst.USER_ID, userId);
        context.setVariable(ContextVarConst.TIMESTAMP, System.currentTimeMillis());
        context.setVariable(ContextVarConst.TRACE_ID, traceId);
        context.setVariable(ContextVarConst.FLOW_TYPE, flowType);
        // 添加ABTest变量
        initializeAbTests(context, abByValue);
        log.debug("Built base context for user: {}", userId);
        return context;
    }
    
    /**
     * 添加用户相关的上下文信息
     *
     * @param context         执行上下文
     * @param userIdentities        用户标签
     */
    protected void enrichUserContext(ExecutionContext context,
                                  Set<String> userIdentities) {
        Map<String, Object> variables = context.getVariables();
        if (userIdentities == null) {
            variables.put(ContextVarConst.USER_IDENTITIES_CONTEXT_KEY, Collections.emptySet());
        } else {
            variables.put(ContextVarConst.USER_IDENTITIES_CONTEXT_KEY, userIdentities);
        }
        log.debug("Enriched user context for user: {}", context.getUserId());
    }
    
    /**
     * 添加设备相关的上下文信息
     *
     * @param context    执行上下文
     * @param source  请求来源
     * @param platform   平台
     * @param realUid    真实设备id
     */
    protected void enrichDeviceContext(ExecutionContext context,
                                    String source,
                                    String platform,
                                    String realUid) {
        Map<String, Object> variables = context.getVariables();
        variables.put(ContextVarConst.SOURCE, source);
        variables.put(ContextVarConst.PLATFORM, platform);
        variables.put(ContextVarConst.REAL_UID, realUid);
        log.debug("Enriched device context for user: {}, source: {}, platform: {}, realUid: {}",
            context.getUserId(), source, platform, realUid);
    }
    
    /**
     * 添加业务相关的上下文信息
     *
     * @param context        执行上下文
     * @param businessParams 业务参数
     */
    protected void enrichBusinessContext(ExecutionContext context,
                                      Map<String, Object> businessParams) {
        if (businessParams != null && !businessParams.isEmpty()) {
            Map<String, Object> variables = context.getVariables();
            variables.putAll(businessParams);
            log.debug("Enriched business context for user: {}, params: {}",
                context.getUserId(), businessParams.keySet());
        }
    }


    /**
     * 初始化用户的AB测试分组
     * @param context 执行上下文
     * @param abByValue 分流值（通常是用户ID）
     */
    public void initializeAbTests(ExecutionContext context, String abByValue) {
        if (abByValue == null || abByValue.isEmpty()) {
            return ;
        }
        ABTestsParam param = new ABTestsParam();
        String traceId = context.getVariableByKey(ContextVarConst.TRACE_ID);
        if (traceId != null && !traceId.isEmpty()) {
            param.putLogParam("traceId", traceId);
        }
        String flowType = context.getVariableByKey(ContextVarConst.FLOW_TYPE);
        if (flowType != null && !flowType.isEmpty()) {
            param.putLogParam("flowType", flowType);
        }
        // 使用注册中心初始化用户在各个实验中的分组
        Map<String, String> abTestResults = AbTestConfigRegistry.getInstance().initializeUserExperiments(abByValue, param);

        if (!abTestResults.isEmpty()) {
            // 将结果放入上下文
            context.setVariable(ContextVarConst.AB_CONTEXT_KEY, abTestResults);
        }
    }

} 