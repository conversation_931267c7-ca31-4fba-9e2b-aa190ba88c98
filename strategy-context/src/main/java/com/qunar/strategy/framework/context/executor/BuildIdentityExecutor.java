package com.qunar.strategy.framework.context.executor;

import com.qunar.strategy.framework.core.annotation.UnitExecutorComponent;
import com.qunar.strategy.framework.core.executor.AbstractUnitExecutor;
import com.qunar.strategy.framework.core.executor.UnitExecutor;
import com.qunar.strategy.framework.common.model.ExecutionContext;
import com.qunar.strategy.framework.common.model.ExecutionUnit;

@UnitExecutorComponent
public class BuildIdentityExecutor<T> extends AbstractUnitExecutor<T> {


    @Override
    public void doExecute(ExecutionContext context, ExecutionUnit unit) throws Exception {

    }

    @Override
    public void fillDebugInfo(ExecutionContext context, ExecutionUnit unit) {
        //execute debug info
    }
}
