package com.qunar.strategy.framework.context;

import com.qunar.strategy.framework.common.model.ExecutionContext;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Set;

@Slf4j
public class StrategyContextService {
    
    private final StrategyContextBuilder contextBuilder;
    
    public StrategyContextService(StrategyContextBuilder contextBuilder) {
        this.contextBuilder = contextBuilder;
    }

    /**
     * @param userId 用户id
     * @param abByValue ab分流的依据值
     * @return ExecutionContext
     */
    public ExecutionContext buildBasicContext(String traceId, String flowType,
                                              String userId, String abByValue, boolean debugMode) {
        return contextBuilder.buildBasicContext(traceId, flowType, userId, abByValue, debugMode);
    }
    
    /**
     * 构建完整的策略执行上下文
     *
     * @param userIdentities 用户身份集合
     * @param deviceInfo 设备信息
     * @param businessParams 业务参数
     */
    public void buildFullContext(ExecutionContext context,
                                             Set<String> userIdentities,
                                             DeviceInfo deviceInfo,
                                             Map<String, Object> businessParams) {
        contextBuilder.enrichUserContext(context, userIdentities);
        // 添加设备信息
        if (deviceInfo != null) {
            contextBuilder.enrichDeviceContext(context, 
                deviceInfo.getSource(),
                deviceInfo.getPlatform(),
                deviceInfo.getRealUid());
        }
        // 添加业务参数
        contextBuilder.enrichBusinessContext(context, businessParams);
    }
    /**
     * 设备信息数据类
     */
    public static class DeviceInfo {
        /**
         * 来源，APP、mini
         */
        private String source;

        /**
         * 设备类型，iOS、android
         * com.qunar.search.common.enums.Platform
         * 值为code，80，90
         */
        private String platform;

        /**
         * 设备id
         */
        private String realUid;

        /**
         * app版本
         */
        private String version;

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public String getPlatform() {
            return platform;
        }

        public void setPlatform(String platform) {
            this.platform = platform;
        }

        public String getRealUid() {
            return realUid;
        }

        public void setRealUid(String realUid) {
            this.realUid = realUid;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }
    }
}
